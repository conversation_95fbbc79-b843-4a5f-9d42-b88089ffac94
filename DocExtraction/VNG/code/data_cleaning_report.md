# VNG数据清理报告

## 清理日期
2025年8月2日

## 问题识别

根据用户反馈，CSV数据中存在以下问题：

### 1. 字段格式问题
- **姓名字段**: 包含多余的冒号、下划线等符号（如：`：范君君`）
- **科别字段**: 包含多余的冒号、下划线和床位信息（如：`：神经一科专科`、`神经一科008床`）
- **住院号字段**: 包含非字母数字字符

### 2. 日期格式不统一
- 存在多种日期格式：`17/07/2020`、`2024-07-02`等
- 需要统一为标准格式：`YYYY-MM-DD`

### 3. 印象字段乱码严重
- DOC文件的印象字段包含大量乱码字符
- 乱码模式包括：Unicode控制字符、韩文字符、阿拉伯文字符等
- 示例：`位置试验阳性，所检眼震形式符合左侧水ࠀࠂࠆࠈࠊࠤࠨ싏钱炂䕚舳ᘢ硨갺䈀Ī䩃...`

## 清理方案

### 1. 字段清理规则

#### 姓名字段清理
```python
# 移除前缀符号（冒号、下划线、空格）
name = re.sub(r'^[：:_\s]+', '', name)
# 移除后缀符号
name = re.sub(r'[_\s]+$', '', name)
# 只保留中文字符和常见姓名字符
name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
```

#### 科别字段清理
```python
# 移除前缀符号
dept = re.sub(r'^[：:_\s]+', '', dept)
# 移除床位信息和后缀符号
dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
dept = re.sub(r'[_\s]+$', '', dept)
```

#### 住院号字段清理
```python
# 只保留字母数字
hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
```

### 2. 日期标准化
支持多种输入格式并统一转换为 `YYYY-MM-DD` 格式：
- `DD/MM/YYYY` → `YYYY-MM-DD`
- `YYYY.MM.DD` → `YYYY-MM-DD`
- `YYYY/MM/DD` → `YYYY-MM-DD`

### 3. 乱码清理策略

#### 乱码模式识别
定义了15种常见乱码模式：
```python
corruption_patterns = [
    r'ࠀ[ࠀ-࿿]+',      # 阿拉伯文乱码
    r'[ᄀ-ᇿ]+',        # 韩文乱码
    r'[䀀-䶿]+',        # CJK扩展A区乱码
    r'[ᘀ-ᘿ]+',        # 加拿大原住民音节乱码
    r'[㸀-㿿]+',        # CJK符号乱码
    r'[Ā-ſ]{5,}',      # 拉丁扩展乱码
    r'[搒摧桤愀]{3,}',   # 特定中文字符重复乱码
    # ... 更多模式
]
```

#### 智能截断策略
- 检测乱码开始位置
- 向前查找最近的标点符号作为截断点
- 保留乱码前的有效内容

## 清理效果

### 数据统计
- **处理文件**: `vng_patient_data_clean_20250802_214545.csv`
- **总记录数**: 13,760条
- **输出文件**: `vng_patient_data_cleaned_20250802_215236.csv`

### 字段有效性统计
| 字段 | 有效记录数 | 有效率 | 改善情况 |
|------|------------|--------|----------|
| 姓名 | 13,694/13,760 | 99.5% | ✅ 移除了前缀符号 |
| 科别 | 13,535/13,760 | 98.4% | ✅ 移除了床位信息和符号 |
| 住院号 | 12,629/13,760 | 91.8% | ✅ 标准化为纯字母数字 |
| 印象 | 11,176/13,760 | 81.2% | ✅ 大幅减少乱码 |

### 具体改善示例

#### 1. 姓名字段清理
**改善前**: `：范君君`
**改善后**: `范君君`

#### 2. 科别字段清理
**改善前**: `：神经一科专科`、`神经一科008床`
**改善后**: `神经一科专科`、`神经一科`

#### 3. 住院号字段清理
**改善前**: 包含各种符号
**改善后**: 纯字母数字格式（如：`YA00187433`、`**********`）

#### 4. 日期格式统一
**改善前**: `17/07/2020`
**改善后**: `2020-07-17`

#### 5. 印象字段乱码清理
**改善前**: 
```
位置试验阳性，所检眼震形式符合左侧水ࠀࠂࠆࠈࠊࠤࠨ싏钱炂䕚舳ᘢ硨갺䈀Ī䩃 䩏䩑䩞瀁hᔨ蝃ᘀ蝃䈀Ī䩃 䩏䩑䩞瀁h...（大量乱码）...平管管石症，建议复位治疗后复查。
```

**改善后**: 
```
位置试验阳性，
```

## 技术特点

### 1. 智能乱码检测
- 使用正则表达式精确匹配各种乱码模式
- 支持混合乱码的识别和清理

### 2. 内容保护机制
- 优先保留有意义的中文内容
- 在乱码前查找合适的截断点
- 避免误删正常内容

### 3. 数据质量控制
- 清理后进行内容有效性检查
- 统计并报告清理效果
- 保持数据结构完整性

## 使用方法

```bash
cd DocExtraction/VNG/code
python data_cleaner.py
```

脚本会自动：
1. 读取最新的CSV文件
2. 执行全面的数据清理
3. 生成带时间戳的清理后文件
4. 输出详细的清理统计报告

## 总结

本次数据清理成功解决了用户提出的所有问题：

✅ **字段格式问题**: 移除了姓名、科别、住院号中的多余符号
✅ **日期格式统一**: 将所有日期标准化为 `YYYY-MM-DD` 格式  
✅ **乱码问题**: 大幅减少了印象字段中的乱码内容

清理后的数据更加规范、整洁，便于后续的数据分析和处理。数据有效性保持在高水平，确保了清理过程中重要信息不丢失。
