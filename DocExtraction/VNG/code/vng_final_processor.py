#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG数据最终处理器
整合了数据提取、清理、格式化的完整流程
"""

import os
import re
import json
import argparse
import pandas as pd
from datetime import datetime
from pathlib import Path
import docx
from docx.shared import RGBColor

class VNGFinalProcessor:
    def __init__(self):
        self.corruption_patterns = [
            r'ࠀ[ࠀ-࿿]+', r'[ᄀ-ᇿ]+', r'[䀀-䶿]+', r'[一-龯]{0,2}[ࠀ-࿿][一-龯]{0,2}',
            r'[ᘀ-ᘿ]+', r'[㸀-㿿]+', r'[Ā-ſ]{5,}', r'[Ĩ-ſ]{3,}', r'[ÿ]{2,}',
            r'[搒摧桤愀]{3,}', r'[혈鐇鐏鐐]{2,}', r'[ӿ]{3,}', r'[Ĥ]{2,}',
            r'[ᡊ伀儀帀]{2,}', r'[瀁桰漀]{2,}'
        ]
    
    def extract_by_patterns(self, text, patterns):
        """使用多个正则模式提取信息"""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return ""
    
    def is_colored_text(self, run):
        """检查文本是否有颜色标记"""
        try:
            if run.font.color and run.font.color.rgb:
                rgb = run.font.color.rgb
                return str(rgb) != 'RGBColor(0x0, 0x0, 0x0)'
            return False
        except:
            return False
    
    def clean_examiner_field(self, examiner_text):
        """清理检查者字段中的乱码"""
        if not examiner_text:
            return ""
        
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,，。、；：！？()（）\[\]【】]', '', examiner_text)
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf]{10,}', '', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        if len(cleaned) < 2 or not re.search(r'[\u4e00-\u9fff]', cleaned):
            return ""
        return cleaned
    
    def extract_patient_info(self, text):
        """提取患者基本信息"""
        info = {}
        
        # 姓名 - 适配WPS文件格式
        name_patterns = [
            r'姓名\s+([^\s]+)\s+性别', r'姓名\s*([^\s]+)\s*性别',
            r'姓名__([^\s]+)', r'姓名[_：:]\s*([^\s_]+)',
            r'姓\s*名\s*[_：:]\s*([^\s_]+)', r'患者姓名\s*[：:]\s*([^\s]+)'
        ]
        info['姓名'] = self.extract_by_patterns(text, name_patterns)
        
        # 性别 - 适配WPS文件格式
        gender_patterns = [
            r'性别\s+([男女])\s+年龄', r'性别\s*([男女])\s*年龄',
            r'性别\s*[_：:]\s*([男女])', r'性\s*别\s*[_：:]\s*([男女])',
            r'患者性别\s*[：:]\s*([男女])'
        ]
        info['性别'] = self.extract_by_patterns(text, gender_patterns)
        
        # 年龄
        age_patterns = [
            r'年龄\s*[_：:]\s*(\d+)', r'年\s*龄\s*[_：:]\s*(\d+)',
            r'(\d+)\s*岁', r'患者年龄\s*[：:]\s*(\d+)'
        ]
        age_str = self.extract_by_patterns(text, age_patterns)
        info['年龄'] = float(age_str) if age_str.isdigit() else None
        
        # 检查日期
        date_patterns = [
            r'检查日期\s*[_：:]\s*(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})',
            r'日期\s*[_：:]\s*(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})',
            r'(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})'
        ]
        info['检查日期'] = self.extract_by_patterns(text, date_patterns)
        
        # 科别 - 适配WPS文件格式
        dept_patterns = [
            r'科别\s+([^\s]+)\s+门诊', r'科别\s*([^\s]+)\s*门诊',
            r'科别\s*[_：:]\s*([^_\s\n]{2,20})', r'科\s*别\s*[_：:]\s*([^_\s\n]{2,20})'
        ]
        info['科别'] = self.extract_by_patterns(text, dept_patterns)
        
        # 门诊/住院号 - 适配WPS文件格式
        id_patterns = [
            r'门诊[/／]住院号\s*([A-Za-z0-9]+)\s*编号',
            r'门诊号[/／]住院号\s*([A-Za-z0-9]+)\s*编号',
            r'门诊[/／]住院号[_：:]\s*([A-Za-z0-9]+)',
            r'住院号[_：:]\s*([A-Za-z0-9]+)', r'门诊号[_：:]\s*([A-Za-z0-9]+)'
        ]
        info['门诊住院号'] = self.extract_by_patterns(text, id_patterns)
        
        # 编号
        code_patterns = [
            r'编号\s*[_：:]\s*(\d+)', r'编\s*号\s*[_：:]\s*(\d+)'
        ]
        info['编号'] = self.extract_by_patterns(text, code_patterns)
        
        return info
    
    def extract_test_results(self, text):
        """提取检查结果"""
        results = {}
        
        # 定标试验
        calibration_patterns = [r'定标试验[：:]\s*([^，。\n]*)', r'定标[：:]\s*([^，。\n]*)']
        results['定标试验'] = self.extract_by_patterns(text, calibration_patterns)
        
        # 自发性眼震
        spontaneous_patterns = [r'自发性眼震[：:]\s*([^，。\n]*)', r'自发眼震[：:]\s*([^，。\n]*)']
        results['自发性眼震'] = self.extract_by_patterns(text, spontaneous_patterns)
        
        # 凝视试验
        gaze_patterns = [r'凝视试验[：:]\s*([^，。\n]*)', r'凝视[：:]\s*([^，。\n]*)']
        results['凝视试验'] = self.extract_by_patterns(text, gaze_patterns)
        
        # 平滑跟踪
        smooth_patterns = [r'平滑跟踪[：:]\s*([^，。\n]*)', r'跟踪[：:]\s*([^，。\n]*)']
        results['平滑跟踪'] = self.extract_by_patterns(text, smooth_patterns)
        
        # 扫视试验
        saccade_patterns = [r'扫视试验[：:]\s*([^，。\n]*)', r'扫视[：:]\s*([^，。\n]*)']
        results['扫视试验'] = self.extract_by_patterns(text, saccade_patterns)
        
        # 视动性眼震
        optokinetic_patterns = [r'视动性眼震[：:]\s*([^，。\n]*)', r'视动[：:]\s*([^，。\n]*)']
        results['视动性眼震'] = self.extract_by_patterns(text, optokinetic_patterns)
        
        # Roll Test
        roll_patterns = [r'Roll[_\s]*Test[：:]\s*([^，。\n]*)', r'翻滚试验[：:]\s*([^，。\n]*)']
        results['Roll_Test'] = self.extract_by_patterns(text, roll_patterns)
        
        # 翻身试验
        turning_patterns = [r'翻身试验[：:]\s*([^，。\n]*)', r'翻身[：:]\s*([^，。\n]*)']
        results['翻身试验'] = self.extract_by_patterns(text, turning_patterns)
        
        # Dix-Hallpike试验
        dix_left_patterns = [r'Dix[-_\s]*Hallpike[_\s]*左侧[：:]\s*([^，。\n]*)', r'左侧.*?Dix.*?[：:]\s*([^，。\n]*)']
        results['Dix_Hallpike_左侧'] = self.extract_by_patterns(text, dix_left_patterns)
        
        dix_right_patterns = [r'Dix[-_\s]*Hallpike[_\s]*右侧[：:]\s*([^，。\n]*)', r'右侧.*?Dix.*?[：:]\s*([^，。\n]*)']
        results['Dix_Hallpike_右侧'] = self.extract_by_patterns(text, dix_right_patterns)
        
        # 疲劳现象
        fatigue_patterns = [r'疲劳现象[：:]\s*([^，。\n]*)', r'疲劳[：:]\s*([^，。\n]*)']
        results['疲劳现象'] = self.extract_by_patterns(text, fatigue_patterns)
        
        # 印象
        impression_patterns = [r'印象[：:]\s*([^\n]*)', r'诊断[：:]\s*([^\n]*)', r'结论[：:]\s*([^\n]*)']
        impression = self.extract_by_patterns(text, impression_patterns)
        results['印象'] = self.clean_impression_field(impression)
        
        return results

    def clean_text_field(self, text):
        """清理文本字段中的乱码和多余符号"""
        if pd.isna(text) or text == '':
            return ''

        text = str(text)

        # 移除乱码
        for pattern in self.corruption_patterns:
            text = re.sub(pattern, '', text)

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        # 如果清理后文本太短或全是乱码，返回空字符串
        if len(text) < 2:
            return ''

        # 检查是否还有大量非中文字符（可能是乱码）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        if total_chars > 10 and chinese_chars / total_chars < 0.3:
            return ''

        return text

    def clean_name_field(self, name):
        """清理姓名字段"""
        if pd.isna(name) or name == '':
            return ''

        name = str(name).strip()
        name = re.sub(r'^[：:_\s]+', '', name)
        name = re.sub(r'[_\s]+$', '', name)
        name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
        return name.strip()

    def clean_department_field(self, dept):
        """清理科别字段"""
        if pd.isna(dept) or dept == '':
            return ''

        dept = str(dept).strip()
        dept = re.sub(r'^[：:_\s]+', '', dept)
        dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
        dept = re.sub(r'[_\s]+$', '', dept)
        return dept.strip()

    def clean_hospital_id(self, hospital_id):
        """清理住院号字段"""
        if pd.isna(hospital_id) or hospital_id == '':
            return ''

        hospital_id = str(hospital_id).strip()
        hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
        return hospital_id

    def standardize_date(self, date_str):
        """标准化日期格式"""
        if pd.isna(date_str) or date_str == '':
            return ''

        date_str = str(date_str).strip()

        # 处理各种日期格式
        date_patterns = [
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', r'\3-\1-\2'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
        ]

        for pattern, replacement in date_patterns:
            if re.match(pattern, date_str):
                date_str = re.sub(pattern, replacement, date_str)
                break

        # 确保月份和日期是两位数
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            try:
                month = f"{int(month):02d}"
                day = f"{int(day):02d}"
                return f"{year}-{month}-{day}"
            except ValueError:
                return date_str

        return date_str

    def clean_impression_field(self, impression):
        """清理印象字段中的乱码"""
        if pd.isna(impression) or impression == '':
            return ''

        impression = str(impression)

        # 查找乱码模式的开始位置
        corruption_start = None
        for pattern in self.corruption_patterns:
            match = re.search(pattern, impression)
            if match:
                if corruption_start is None or match.start() < corruption_start:
                    corruption_start = match.start()

        # 如果找到乱码，截取乱码前的部分
        if corruption_start is not None:
            clean_end = corruption_start
            for i in range(corruption_start - 1, -1, -1):
                if impression[i] in '。，；、':
                    clean_end = i + 1
                    break
                elif impression[i] in '.,;':
                    clean_end = i + 1
                    break
            impression = impression[:clean_end]

        # 移除剩余的乱码
        for pattern in self.corruption_patterns:
            impression = re.sub(pattern, '', impression)

        # 清理多余的空白字符
        impression = re.sub(r'\s+', ' ', impression).strip()
        impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】]+$', '', impression)

        return impression.strip()

    def extract_from_docx(self, file_path):
        """从DOCX文件提取数据"""
        try:
            doc = docx.Document(file_path)

            # 提取所有文本
            full_text = []
            for paragraph in doc.paragraphs:
                full_text.append(paragraph.text)

            text = '\n'.join(full_text)

            # 提取患者信息和检查结果
            patient_info = self.extract_patient_info(text)
            test_results = self.extract_test_results(text)

            # 合并结果
            result = {**patient_info, **test_results}
            result['原始文本'] = text

            return result

        except Exception as e:
            print(f"处理DOCX文件 {file_path} 时出错: {str(e)}")
            return None

    def extract_from_wps(self, file_path):
        """从WPS文件提取数据（使用docx库）"""
        try:
            # WPS文件通常可以用docx库读取
            doc = docx.Document(file_path)

            # 提取所有文本
            full_text = []
            for paragraph in doc.paragraphs:
                full_text.append(paragraph.text)

            text = '\n'.join(full_text)

            # 提取患者信息和检查结果
            patient_info = self.extract_patient_info(text)
            test_results = self.extract_test_results(text)

            # 合并结果
            result = {**patient_info, **test_results}
            result['原始文本'] = text

            return result

        except Exception as e:
            print(f"处理WPS文件 {file_path} 时出错: {str(e)}")
            return None

    def extract_from_doc(self, file_path):
        """从DOC文件提取数据"""
        try:
            # 对于DOC文件，尝试使用docx库
            doc = docx.Document(file_path)

            # 提取所有文本
            full_text = []
            for paragraph in doc.paragraphs:
                full_text.append(paragraph.text)

            text = '\n'.join(full_text)

            # 提取患者信息和检查结果
            patient_info = self.extract_patient_info(text)
            test_results = self.extract_test_results(text)

            # 合并结果
            result = {**patient_info, **test_results}
            result['原始文本'] = text

            return result

        except Exception as e:
            print(f"处理DOC文件 {file_path} 时出错: {str(e)}")
            return None

    def process_file(self, file_path):
        """处理单个文件"""
        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.docx':
            return self.extract_from_docx(file_path)
        elif file_ext == '.wps':
            return self.extract_from_wps(file_path)
        elif file_ext == '.doc':
            return self.extract_from_doc(file_path)
        else:
            print(f"不支持的文件格式: {file_ext}")
            return None

    def process_directory(self, source_dir, output_dir):
        """处理整个目录"""
        source_path = Path(source_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 支持的文件扩展名
        supported_extensions = ['.docx', '.doc', '.wps']

        # 查找所有支持的文件
        all_files = []
        for ext in supported_extensions:
            all_files.extend(source_path.rglob(f'*{ext}'))

        print(f"找到 {len(all_files)} 个文件")

        # 处理文件
        results = []
        processed_count = 0
        failed_count = 0

        for file_path in all_files:
            print(f"处理文件: {file_path.name}")

            result = self.process_file(file_path)
            if result:
                result['文件名'] = file_path.name

                # 从文件名提取年份
                year_match = re.search(r'(\d{4})', file_path.name)
                if year_match:
                    result['年份'] = float(year_match.group(1))
                else:
                    result['年份'] = None

                results.append(result)
                processed_count += 1
            else:
                failed_count += 1

        print(f"处理完成: 成功 {processed_count}, 失败 {failed_count}")

        # 转换为DataFrame
        if results:
            df = pd.DataFrame(results)

            # 重新排列列的顺序
            columns_order = [
                '文件名', '年份', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
                '定标试验', '自发性眼震', '自发性眼震_水平向左', '自发性眼震_水平向右',
                '自发性眼震_垂直向上', '自发性眼震_垂直向下',
                '凝视试验', '平滑跟踪', '扫视试验', '视动性眼震',
                'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象',
                '印象', '原始文本'
            ]

            # 确保所有列都存在
            for col in columns_order:
                if col not in df.columns:
                    df[col] = ''

            df = df[columns_order]

            # 保存原始提取结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            raw_output_file = output_path / f'vng_patient_data_raw_{timestamp}.csv'
            df.to_csv(raw_output_file, index=False, encoding='utf-8-sig')
            print(f"原始数据已保存到: {raw_output_file}")

            return df
        else:
            print("没有成功处理任何文件")
            return None

    def clean_and_finalize_data(self, df, output_dir):
        """清理数据并生成最终CSV"""
        output_path = Path(output_dir)

        print("开始数据清理...")

        # 清理各个字段
        if '姓名' in df.columns:
            print("清理姓名字段...")
            df['姓名'] = df['姓名'].apply(self.clean_name_field)

        if '科别' in df.columns:
            print("清理科别字段...")
            df['科别'] = df['科别'].apply(self.clean_department_field)

        if '门诊住院号' in df.columns:
            print("清理住院号字段...")
            df['门诊住院号'] = df['门诊住院号'].apply(self.clean_hospital_id)

        if '检查日期' in df.columns:
            print("标准化检查日期...")
            df['检查日期'] = df['检查日期'].apply(self.standardize_date)

        if '印象' in df.columns:
            print("清理印象字段...")
            df['印象'] = df['印象'].apply(self.clean_impression_field)

        # 移除年份列和原始文本列
        columns_to_remove = ['年份', '原始文本']
        for col in columns_to_remove:
            if col in df.columns:
                print(f"移除{col}列...")
                df = df.drop(col, axis=1)

        # 最终列顺序（不包含年份和原始文本）
        final_columns = [
            '文件名', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
            '定标试验', '自发性眼震', '自发性眼震_水平向左', '自发性眼震_水平向右',
            '自发性眼震_垂直向上', '自发性眼震_垂直向下',
            '凝视试验', '平滑跟踪', '扫视试验', '视动性眼震',
            'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象',
            '印象'
        ]

        # 确保所有列都存在并按顺序排列
        for col in final_columns:
            if col not in df.columns:
                df[col] = ''

        df = df[final_columns]

        # 保存最终清理后的数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_output_file = output_path / f'vng_patient_data_final_{timestamp}.csv'
        df.to_csv(final_output_file, index=False, encoding='utf-8-sig')

        print(f"最终数据已保存到: {final_output_file}")

        # 打印统计信息
        self.print_final_stats(df)

        return df, final_output_file

    def print_final_stats(self, df):
        """打印最终统计信息"""
        print("\n=== 最终数据统计 ===")
        print(f"总记录数: {len(df)}")
        print(f"总字段数: {len(df.columns)}")

        # 字段有效性统计
        key_fields = ['姓名', '性别', '科别', '门诊住院号', '检查日期', '印象']
        for field in key_fields:
            if field in df.columns:
                empty_count = df[field].isna().sum() + (df[field] == '').sum()
                valid_count = len(df) - empty_count
                valid_rate = (valid_count / len(df)) * 100
                print(f"{field}: {valid_count}/{len(df)} ({valid_rate:.1f}%)")

    def run_complete_process(self, source_dir, output_dir):
        """运行完整的数据处理流程"""
        print("=== VNG数据完整处理流程 ===")
        print(f"源目录: {source_dir}")
        print(f"输出目录: {output_dir}")

        # 步骤1: 提取数据
        print("\n步骤1: 提取数据...")
        df = self.process_directory(source_dir, output_dir)

        if df is None:
            print("数据提取失败，流程终止")
            return None

        # 步骤2: 清理和最终化数据
        print("\n步骤2: 清理和最终化数据...")
        final_df, final_file = self.clean_and_finalize_data(df, output_dir)

        print(f"\n=== 处理完成 ===")
        print(f"最终文件: {final_file}")
        print(f"记录数: {len(final_df)}")
        print(f"字段数: {len(final_df.columns)}")

        return final_df, final_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VNG数据最终处理器')
    parser.add_argument('--source-dir', default='../data', help='源数据目录')
    parser.add_argument('--output-dir', default='../output', help='输出目录')

    args = parser.parse_args()

    # 创建处理器实例
    processor = VNGFinalProcessor()

    # 运行完整流程
    result = processor.run_complete_process(args.source_dir, args.output_dir)

    if result:
        print("\n数据处理成功完成！")
    else:
        print("\n数据处理失败！")

if __name__ == "__main__":
    main()
