#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
移除CSV文件中的"年份"列
"""

import pandas as pd
import os
from datetime import datetime

def remove_year_column(input_file, output_file):
    """移除CSV文件中的年份列"""
    print(f"读取文件: {input_file}")
    
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
    print(f"原始列名: {list(df.columns)}")
    
    # 检查是否存在"年份"列
    if '年份' in df.columns:
        # 移除年份列
        df = df.drop('年份', axis=1)
        print(f"已移除'年份'列")
    else:
        print("未找到'年份'列")
    
    print(f"处理后数据: {len(df)} 行, {len(df.columns)} 列")
    print(f"处理后列名: {list(df.columns)}")
    
    # 保存处理后的数据
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"处理后的数据已保存到: {output_file}")
    
    return df

def main():
    """主函数"""
    # 输入和输出文件路径
    input_file = '../output/vng_patient_data_cleaned_20250802_215236.csv'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'../output/vng_patient_data_final_{timestamp}.csv'
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    # 执行移除年份列操作
    df = remove_year_column(input_file, output_file)
    
    print("\n移除年份列完成!")
    print(f"最终文件列数: {len(df.columns)}")

if __name__ == "__main__":
    main()
