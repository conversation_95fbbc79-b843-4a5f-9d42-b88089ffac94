# VNG数据最终处理器

## 概述

`vng_final_processor.py` 是一个统一的VNG患者数据处理工具，整合了数据提取、清理、格式化的完整流程。

## 功能特点

### ✅ 完整的数据处理流程
- **数据提取**: 支持 `.docx`、`.doc`、`.wps` 格式文件
- **字段清理**: 自动清理姓名、科别、住院号等字段中的多余符号
- **日期标准化**: 统一日期格式为 `YYYY-MM-DD`
- **乱码清理**: 智能识别并清理印象字段中的乱码
- **结构优化**: 自动移除年份列，生成24字段的标准CSV

### ✅ 智能数据提取
- **WPS文件优化**: 特别优化了WPS文件的字段提取
- **多格式支持**: 统一处理不同格式的医疗文档
- **错误处理**: 完善的异常处理机制

### ✅ 数据质量保证
- **格式标准化**: 统一字段格式
- **内容保护**: 避免误删有效信息
- **质量统计**: 自动生成数据质量报告

## 使用方法

### 基本用法
```bash
python vng_final_processor.py --source-dir ../data --output-dir ../output
```

### 参数说明
- `--source-dir`: 源数据目录（默认: `../data`）
- `--output-dir`: 输出目录（默认: `../output`）

## 输出文件

### 最终CSV文件
- **文件名**: `vng_patient_data_final_YYYYMMDD_HHMMSS.csv`
- **字段数**: 24个
- **编码**: UTF-8 with BOM

### 字段列表
```
1. 文件名           13. 凝视试验
2. 姓名             14. 平滑跟踪  
3. 性别             15. 扫视试验
4. 年龄             16. 视动性眼震
5. 检查日期         17. Roll_Test
6. 科别             18. 翻身试验
7. 门诊住院号       19. Dix_Hallpike_左侧
8. 编号             20. Dix_Hallpike_右侧
9. 定标试验         21. 疲劳现象
10. 自发性眼震      22. 印象
11. 自发性眼震_水平向左
12. 自发性眼震_水平向右
```

## 处理流程

### 步骤1: 数据提取
- 扫描源目录中的所有支持格式文件
- 使用优化的正则表达式提取患者信息和检查结果
- 生成原始数据CSV（包含原始文本）

### 步骤2: 数据清理
- 清理姓名字段（移除前缀符号）
- 清理科别字段（移除床位信息）
- 标准化住院号格式（纯字母数字）
- 统一日期格式
- 清理印象字段乱码

### 步骤3: 最终化
- 移除年份列和原始文本列
- 按标准顺序排列字段
- 生成最终清洁版CSV

## 数据质量

### 典型处理效果
- **姓名字段**: 98%+ 有效率
- **性别字段**: 99%+ 有效率  
- **科别字段**: 99%+ 有效率
- **检查日期**: 96%+ 有效率
- **住院号**: 76%+ 有效率
- **印象字段**: 100% 有效率（清理后）

### 格式标准化
- ✅ 日期格式: 100% 统一为 `YYYY-MM-DD`
- ✅ 姓名格式: 100% 移除多余符号
- ✅ 科别格式: 100% 移除床位信息
- ✅ 住院号: 100% 纯字母数字格式

## 技术特点

### 智能乱码处理
- 识别15种常见乱码模式
- 智能截断：在乱码前的标点处截断
- 内容保护：避免误删正常文本

### 优化的字段提取
- 针对WPS文件的特殊格式优化
- 多种正则表达式模式匹配
- 容错性强的数据提取

### 自动化处理
- 一键完成完整流程
- 自动生成处理报告
- 完善的错误处理

## 示例输出

```
=== VNG数据完整处理流程 ===
源目录: ../data
输出目录: ../output

步骤1: 提取数据...
找到 3313 个文件
处理完成: 成功 3313, 失败 0

步骤2: 清理和最终化数据...
清理姓名字段...
清理科别字段...
清理住院号字段...
标准化检查日期...
清理印象字段...
移除年份列...
移除原始文本列...

=== 最终数据统计 ===
总记录数: 3313
总字段数: 24
姓名: 3265/3313 (98.6%)
性别: 3297/3313 (99.5%)
科别: 3283/3313 (99.1%)
住院号: 2519/3313 (76.0%)
检查日期: 3205/3313 (96.7%)
印象: 3313/3313 (100.0%)

=== 处理完成 ===
最终文件: ../output/vng_patient_data_final_20250802_220628.csv
```

## 注意事项

1. **文件格式**: 确保源文件为 `.docx`、`.doc` 或 `.wps` 格式
2. **目录结构**: 程序会递归扫描源目录的所有子目录
3. **内存使用**: 大量文件处理时注意内存使用情况
4. **编码问题**: 输出文件使用UTF-8 with BOM编码，确保Excel正确显示中文

## 更新历史

- **v1.0**: 统一整合所有数据处理功能
- 移除了多个分散的脚本文件
- 优化了WPS文件处理
- 完善了乱码清理机制
- 自动化了完整处理流程
