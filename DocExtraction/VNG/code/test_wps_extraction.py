#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试WPS文件提取功能
"""

import os
from vng_data_extractor import VNGDataExtractor

def test_wps_extraction():
    """测试WPS文件提取"""
    print("测试WPS文件提取功能")
    print("=" * 50)
    
    # 测试几个WPS文件
    test_files = [
        "../data/final_vng_classified/2022/5360黎运容.wps",
        "../data/final_vng_classified/2022/6800施慧仑.wps",
        "../data/final_vng_classified/2022/5733苏敏聪.wps"
    ]
    
    output_dir = "../test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    extractor = VNGDataExtractor("", output_dir, debug=True)
    
    for file_path in test_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            print(f"\n处理文件: {filename}")
            print("-" * 40)
            
            try:
                # 直接测试文本提取
                text = extractor.extract_text_from_file(file_path)
                print(f"提取文本长度: {len(text)} 字符")
                
                if text.strip():
                    print("文本前500字符:")
                    print(text[:500])
                    print("...")
                    
                    # 检查是否包含关键信息
                    keywords = ['姓名', '性别', '年龄', '检查日期', 'VNG', '视频眼震']
                    found_keywords = [kw for kw in keywords if kw in text]
                    print(f"找到关键词: {found_keywords}")
                    
                    # 尝试提取患者数据
                    patient_data = extractor.extract_patient_data(file_path)
                    if patient_data:
                        print("\n提取的数据:")
                        for key, value in patient_data.items():
                            if key != '原始文本' and value:
                                print(f"  {key}: {value}")
                    else:
                        print("无法提取患者数据")
                else:
                    print("未提取到有效文本")
                    
            except Exception as e:
                print(f"处理异常: {e}")
                import traceback
                print(traceback.format_exc())
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    test_wps_extraction()
