#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG数据简单清理器
基于已经成功的CSV文件，只做数据清理，不重新提取
保持原有逻辑完全不变
"""

import pandas as pd
import re
import os
from datetime import datetime

class VNGSimpleCleaner:
    def __init__(self):
        # 原有的乱码清理模式
        self.corruption_patterns = [
            r'ࠀ[ࠀ-࿿]+', r'[ᄀ-ᇿ]+', r'[䀀-䶿]+', r'[一-龯]{0,2}[ࠀ-࿿][一-龯]{0,2}',
            r'[ᘀ-ᘿ]+', r'[㸀-㿿]+', r'[Ā-ſ]{5,}', r'[Ĩ-ſ]{3,}', r'[ÿ]{2,}',
            r'[搒摧桤愀]{3,}', r'[혈鐇鐏鐐]{2,}', r'[ӿ]{3,}', r'[Ĥ]{2,}',
            r'[ᡊ伀儀帀]{2,}', r'[瀁桰漀]{2,}'
        ]
    
    def clean_name_field(self, name):
        """清理姓名字段"""
        if pd.isna(name) or name == '':
            return ''
        
        name = str(name).strip()
        name = re.sub(r'^[：:_\s]+', '', name)
        name = re.sub(r'[_\s]+$', '', name)
        name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
        return name.strip()
    
    def clean_department_field(self, dept):
        """清理科别字段"""
        if pd.isna(dept) or dept == '':
            return ''
        
        dept = str(dept).strip()
        dept = re.sub(r'^[：:_\s]+', '', dept)
        dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
        dept = re.sub(r'[_\s]+$', '', dept)
        return dept.strip()
    
    def clean_hospital_id(self, hospital_id):
        """清理住院号字段"""
        if pd.isna(hospital_id) or hospital_id == '':
            return ''
        
        hospital_id = str(hospital_id).strip()
        hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
        return hospital_id
    
    def standardize_date(self, date_str):
        """标准化日期格式"""
        if pd.isna(date_str) or date_str == '':
            return ''
        
        date_str = str(date_str).strip()
        
        # 处理各种日期格式
        date_patterns = [
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', r'\3-\1-\2'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
        ]
        
        for pattern, replacement in date_patterns:
            if re.match(pattern, date_str):
                date_str = re.sub(pattern, replacement, date_str)
                break
        
        # 确保月份和日期是两位数
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            try:
                month = f"{int(month):02d}"
                day = f"{int(day):02d}"
                return f"{year}-{month}-{day}"
            except ValueError:
                return date_str
        
        return date_str
    
    def clean_impression_field(self, impression):
        """清理印象字段中的乱码"""
        if pd.isna(impression) or impression == '':
            return ''
        
        impression = str(impression)
        
        # 查找乱码模式的开始位置
        corruption_start = None
        for pattern in self.corruption_patterns:
            match = re.search(pattern, impression)
            if match:
                if corruption_start is None or match.start() < corruption_start:
                    corruption_start = match.start()
        
        # 如果找到乱码，截取乱码前的部分
        if corruption_start is not None:
            clean_end = corruption_start
            for i in range(corruption_start - 1, -1, -1):
                if impression[i] in '。，；、':
                    clean_end = i + 1
                    break
                elif impression[i] in '.,;':
                    clean_end = i + 1
                    break
            impression = impression[:clean_end]
        
        # 移除剩余的乱码
        for pattern in self.corruption_patterns:
            impression = re.sub(pattern, '', impression)
        
        # 清理多余的空白字符
        impression = re.sub(r'\s+', ' ', impression).strip()
        impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】]+$', '', impression)
        
        return impression.strip()
    
    def clean_csv_data(self, input_file, output_file):
        """清理CSV数据"""
        print(f"读取文件: {input_file}")
        df = pd.read_csv(input_file)
        
        print(f"原始数据: {len(df)} 行, {len(df.columns)} 列")
        
        # 清理各个字段
        if '姓名' in df.columns:
            print("清理姓名字段...")
            df['姓名'] = df['姓名'].apply(self.clean_name_field)
        
        if '科别' in df.columns:
            print("清理科别字段...")
            df['科别'] = df['科别'].apply(self.clean_department_field)
        
        if '门诊住院号' in df.columns:
            print("清理住院号字段...")
            df['门诊住院号'] = df['门诊住院号'].apply(self.clean_hospital_id)
        
        if '检查日期' in df.columns:
            print("标准化检查日期...")
            df['检查日期'] = df['检查日期'].apply(self.standardize_date)
        
        if '印象' in df.columns:
            print("清理印象字段...")
            df['印象'] = df['印象'].apply(self.clean_impression_field)
        
        # 移除年份列（如果存在）
        if '年份' in df.columns:
            print("移除年份列...")
            df = df.drop('年份', axis=1)
        
        # 移除检查者列（如果存在）
        if '检查者' in df.columns:
            print("移除检查者列...")
            df = df.drop('检查者', axis=1)
        
        # 保存清理后的数据
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"清理后的数据已保存到: {output_file}")
        
        # 统计清理效果
        self.print_cleaning_stats(df)
        
        return df
    
    def print_cleaning_stats(self, df):
        """打印清理统计信息"""
        print("\n=== 清理统计 ===")
        print(f"总记录数: {len(df)}")
        print(f"总字段数: {len(df.columns)}")
        
        key_fields = ['姓名', '性别', '科别', '门诊住院号', '检查日期', '印象']
        for field in key_fields:
            if field in df.columns:
                empty_count = df[field].isna().sum() + (df[field] == '').sum()
                valid_count = len(df) - empty_count
                valid_rate = (valid_count / len(df)) * 100
                print(f"{field}: {valid_count}/{len(df)} ({valid_rate:.1f}%)")

def main():
    """主函数"""
    cleaner = VNGSimpleCleaner()
    
    # 使用已经成功的CSV文件作为输入
    input_file = '../output/vng_patient_data_final_20250802_215918.csv'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f'../output/vng_patient_data_cleaned_{timestamp}.csv'
    
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        print("请确保已经有成功的CSV文件")
        return
    
    # 执行清理
    df = cleaner.clean_csv_data(input_file, output_file)
    
    print(f"\n=== 处理完成 ===")
    print(f"最终文件: {output_file}")
    print(f"记录数: {len(df)}")
    print(f"字段数: {len(df.columns)}")

if __name__ == "__main__":
    main()
