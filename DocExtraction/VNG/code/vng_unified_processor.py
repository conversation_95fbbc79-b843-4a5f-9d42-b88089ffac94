#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VNG病例信息提取系统

此脚本用于从已分类的VNG病例文档中提取完整的患者信息和检查数据，
包括患者基本信息、VNG检查项目结果、位置试验结果和医生诊断等。

使用方法:
    python vng_data_extractor.py [--source-dir SOURCE_DIR] [--output-dir OUTPUT_DIR] [--debug]
    
作者: Assistant
日期: 2025-01-02
"""

import os
import re
import sys
import csv
import shutil
import argparse
import traceback
from pathlib import Path
from datetime import datetime
import docx
import olefile


class VNGDataExtractor:
    """VNG病例数据提取器"""
    
    def __init__(self, source_dir, output_dir, debug=False):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.debug = debug
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'cannot_extract'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'failed_files', 'format_error'), exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'success_count': 0,
            'failed_count': 0,
            'cannot_extract': 0,
            'format_error': 0,
            'year_stats': {}
        }
        
        # 日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(output_dir, f'processing_log_{timestamp}.txt')
        self.csv_file = os.path.join(output_dir, f'vng_patient_data_{timestamp}.csv')

        # 乱码清理模式
        self.corruption_patterns = [
            r'ࠀ[ࠀ-࿿]+', r'[ᄀ-ᇿ]+', r'[䀀-䶿]+', r'[一-龯]{0,2}[ࠀ-࿿][一-龯]{0,2}',
            r'[ᘀ-ᘿ]+', r'[㸀-㿿]+', r'[Ā-ſ]{5,}', r'[Ĩ-ſ]{3,}', r'[ÿ]{2,}',
            r'[搒摧桤愀]{3,}', r'[혈鐇鐏鐐]{2,}', r'[ӿ]{3,}', r'[Ĥ]{2,}',
            r'[ᡊ伀儀帀]{2,}', r'[瀁桰漀]{2,}'
        ]
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        if self.debug:
            print(log_message)
            
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def extract_text_from_file(self, file_path):
        """从文件中提取文本内容，包括颜色标记的选项"""
        file_ext = os.path.splitext(file_path)[1].lower()
        full_text = ""
        colored_selections = {}  # 存储颜色标记的选项

        try:
            if file_ext == '.docx':
                doc = docx.Document(file_path)

                # 提取段落文本，同时检查颜色标记
                for paragraph in doc.paragraphs:
                    para_text = paragraph.text
                    full_text += para_text + "\n"

                    # 检查颜色标记的选项
                    colored_options = self.extract_colored_options(paragraph)
                    if colored_options:
                        colored_selections.update(colored_options)

                # 提取表格文本
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            full_text += cell.text + " "
                    full_text += "\n"
                    
            elif file_ext in ['.doc', '.wps']:
                # 首先尝试用docx方式读取（某些WPS文件可能兼容）
                try:
                    doc = docx.Document(file_path)
                    # 提取段落文本，同时检查颜色标记
                    for paragraph in doc.paragraphs:
                        para_text = paragraph.text
                        full_text += para_text + "\n"

                        # 检查颜色标记的选项
                        colored_options = self.extract_colored_options(paragraph)
                        if colored_options:
                            colored_selections.update(colored_options)

                    # 提取表格文本
                    for table in doc.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                full_text += cell.text + " "
                        full_text += "\n"

                except Exception:
                    # 如果docx方式失败，使用olefile方式
                    if olefile.isOleFile(file_path):
                        ole = olefile.OleFileIO(file_path)

                        # 尝试读取文档内容流，使用更多编码方式
                        priority_streams = ['WordDocument', '1Table', '0Table', 'Data', 'CompObj']

                        for stream_name in priority_streams:
                            try:
                                if ole.exists(stream_name):
                                    stream_data = ole.openstream(stream_name).read()

                                    # 尝试多种编码方式和文本清理
                                    best_text = ""
                                    best_score = 0

                                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-16le', 'utf-16be', 'latin1']:
                                        try:
                                            text_data = stream_data.decode(encoding, errors='ignore')

                                            # 改进的文本清理
                                            clean_text = self.clean_extracted_text(text_data)

                                            # 评分：包含的关键词越多分数越高
                                            score = sum(1 for keyword in ['姓名', '性别', '年龄', '检查日期', 'VNG', '视频眼震', '定标试验', '自发性眼震']
                                                       if keyword in clean_text)

                                            if score > best_score:
                                                best_score = score
                                                best_text = clean_text

                                        except Exception:
                                            continue

                                    if best_text and best_score > 0:
                                        full_text += best_text

                                    if full_text.strip():
                                        break
                            except Exception:
                                continue
                        ole.close()
                    
        except Exception as e:
            self.log(f"提取文本失败 {file_path}: {e}")
            return ""
            
        # 将颜色标记的选项合并到文本中
        if colored_selections:
            full_text += "\n=== 颜色标记选项 ===\n"
            for key, value in colored_selections.items():
                full_text += f"{key}: {value}\n"

        return full_text

    def clean_extracted_text(self, raw_text):
        """清理从WPS/DOC文件中提取的文本"""
        import re

        # 移除控制字符，但保留中文字符和基本标点
        cleaned = ""
        for char in raw_text:
            # 保留中文字符、英文字母、数字、基本标点和空白字符
            if (char.isalnum() or
                char in '，。：；！？（）【】""''、/\\-_=+<>%°/s．' or
                char.isspace() or
                '\u4e00' <= char <= '\u9fff'):  # 中文字符范围
                cleaned += char
            elif ord(char) < 32:  # 跳过控制字符
                continue

        # 清理多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # 移除明显的乱码模式
        cleaned = re.sub(r'[^\u4e00-\u9fff\w\s，。：；！？（）【】""''、/\\-_=+<>%°．]+', ' ', cleaned)

        # 移除重复的特殊字符序列
        cleaned = re.sub(r'([^\u4e00-\u9fff\w\s])\1{3,}', r'\1', cleaned)

        # 移除过短的垃圾片段
        lines = cleaned.split('\n')
        valid_lines = []
        for line in lines:
            line = line.strip()
            # 只保留包含中文或有意义内容的行
            if (len(line) > 2 and
                (any('\u4e00' <= char <= '\u9fff' for char in line) or  # 包含中文
                 any(keyword in line for keyword in ['VNG', 'Roll', 'Test', 'Dix']))):  # 包含关键词
                valid_lines.append(line)

        return '\n'.join(valid_lines)

    def clean_examiner_field(self, examiner_text):
        """清理检查者字段中的乱码"""
        import re

        # 只保留中文字符、英文字母和基本标点
        cleaned = ""
        for char in examiner_text:
            if (char.isalpha() or
                char in '，。：；（）' or
                char.isspace() or
                '\u4e00' <= char <= '\u9fff'):  # 中文字符范围
                cleaned += char

        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 如果清理后太短或全是乱码，返回空字符串
        if len(cleaned) < 2 or not any('\u4e00' <= char <= '\u9fff' for char in cleaned):
            return ""

        return cleaned

    def extract_colored_options(self, paragraph):
        """提取段落中颜色标记的选项"""
        colored_options = {}
        para_text = paragraph.text

        # 检查是否包含选项格式
        option_patterns = [
            r'(定标试验.*?)(正常|异常)',
            r'(自发性眼震.*?)(无|有)',
            r'(凝视试验.*?)(正常|异常)',
            r'(平滑跟踪.*?)(Ⅰ型|Ⅱ型|Ⅲ型|Ⅳ型|I型|II型|III型|IV型)',
            r'(扫视试验.*?)(正常|异常)',
            r'(视动性眼震.*?)(对称|不对称)',
            r'(Roll Test.*?)(阴性|阳性)',
            r'(翻身试验.*?)(阴性|阳性)',
            r'(左侧悬头位.*?)(阴性|阳性)',
            r'(右侧悬头位.*?)(阴性|阳性)',
            r'(疲劳现象.*?)(阴性|阳性)',
        ]

        for pattern in option_patterns:
            match = re.search(pattern, para_text)
            if match:
                test_name = match.group(1).strip()

                # 检查每个run的颜色
                for run in paragraph.runs:
                    if run.text.strip() and self.is_colored_text(run):
                        # 检查这个run的文本是否包含选项
                        for option in ['正常', '异常', '无', '有', 'Ⅰ型', 'Ⅱ型', 'Ⅲ型', 'Ⅳ型',
                                     'I型', 'II型', 'III型', 'IV型', '对称', '不对称', '阴性', '阳性']:
                            if option in run.text:
                                colored_options[test_name] = option
                                break

        return colored_options

    def is_colored_text(self, run):
        """检查文本是否有颜色标记"""
        try:
            if run.font.color and run.font.color.rgb:
                # 检查是否不是默认黑色
                rgb = run.font.color.rgb
                # 如果RGB值不是黑色(0,0,0)，则认为是有颜色的
                return str(rgb) != 'RGBColor(0x0, 0x0, 0x0)'
            return False
        except:
            return False

    def clean_examiner_field(self, examiner_text):
        """清理检查者字段中的乱码"""
        if not examiner_text:
            return ""

        # 移除常见的乱码字符
        # 保留中文字符、英文字符、数字和常见标点
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s\.,，。、；：！？()（）\[\]【】]', '', examiner_text)

        # 移除过长的连续乱码（超过10个字符的非中文字符串）
        cleaned = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf]{10,}', '', cleaned)

        # 清理多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # 如果清理后的内容太短或者全是乱码，返回空字符串
        if len(cleaned) < 2 or not re.search(r'[\u4e00-\u9fff]', cleaned):
            return ""

        return cleaned
    
    def extract_by_patterns(self, text, patterns):
        """使用多个正则表达式模式提取信息"""
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return ""
    
    def extract_basic_info(self, text):
        """提取患者基本信息"""
        info = {}

        # 姓名 - 适配多种格式，包括WPS文件中的格式
        name_patterns = [
            r'姓名\s+([^\s]+)\s+性别',  # 姓名 苏敏聪 性别
            r'姓名\s*([^\s]+)\s*性别',  # 姓名苏敏聪性别
            r'姓名__([^\s]+)',  # 姓名__梁趣轩
            r'姓名[_：:]\s*([^\s_]+)',  # 姓名_张三 或 姓名：张三
            r'姓\s*名\s*[_：:]\s*([^\s_]+)',
            r'患者姓名\s*[：:]\s*([^\s]+)',
        ]
        info['姓名'] = self.extract_by_patterns(text, name_patterns)

        # 性别 - 适配WPS文件格式
        gender_patterns = [
            r'性别\s+([男女])\s+年龄',  # 性别 男 年龄
            r'性别\s*([男女])\s*年龄',  # 性别男年龄
            r'性别\s*[_：:]\s*([男女])',  # 性别  _女
            r'性\s*别\s*[_：:]\s*([男女])',
            r'患者性别\s*[：:]\s*([男女])',
        ]
        info['性别'] = self.extract_by_patterns(text, gender_patterns)

        # 年龄 - 适配下划线和岁字格式
        age_patterns = [
            r'年龄\s*[_：:]\s*(\d+)[岁]?',  # 年龄  72岁_
            r'年\s*龄\s*[_：:]\s*(\d+)[岁]?',
            r'(\d+)岁',  # 直接匹配数字+岁
        ]
        info['年龄'] = self.extract_by_patterns(text, age_patterns)

        # 检查日期
        date_patterns = [
            r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'检查日期[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
            r'(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})',
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
        ]
        info['检查日期'] = self.extract_by_patterns(text, date_patterns)

        # 科别 - 适配WPS文件格式
        dept_patterns = [
            r'科别\s+([^\s]+)\s+门诊',  # 科别 神经一科专科 门诊
            r'科别\s*([^\s]+)\s*门诊',  # 科别神经一科专科门诊
            r'科别\s*[_：:]\s*([^_\s\n]{2,20})',  # 科别    _神经一科23床_
            r'科\s*别\s*[_：:]\s*([^_\s\n]{2,20})',
        ]
        info['科别'] = self.extract_by_patterns(text, dept_patterns)

        # 门诊/住院号 - 适配WPS文件格式
        id_patterns = [
            r'门诊[/／]住院号\s*([A-Za-z0-9]+)\s*编号',  # 门诊/住院号0000813951 编号
            r'门诊号[/／]住院号\s*([A-Za-z0-9]+)\s*编号',  # 门诊号/住院号0001389040编号
            r'门诊[/／]住院号[_：:]\s*([A-Za-z0-9]+)',  # 门诊/住院号_ 200312 _
            r'住院号[_：:]\s*([A-Za-z0-9]+)',
            r'门诊号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['门诊住院号'] = self.extract_by_patterns(text, id_patterns)

        # 编号
        number_patterns = [
            r'编\s*号\s*[_：:]\s*([A-Za-z0-9]+)',
            r'编号[_：:]\s*([A-Za-z0-9]+)',
        ]
        info['编号'] = self.extract_by_patterns(text, number_patterns)

        return info
    
    def extract_examination_results(self, text):
        """提取检查结果"""
        results = {}
        
        # 定标试验
        calibration_patterns = [
            r'定标试验\s*([正异]常)',
            r'1、定标试验\s*([正异]常)',
        ]
        results['定标试验'] = self.extract_by_patterns(text, calibration_patterns)
        
        # 自发性眼震
        spontaneous_patterns = [
            r'自发性眼震\s*([无有])',
            r'2、自发性眼震\s*([无有])',
        ]
        results['自发性眼震'] = self.extract_by_patterns(text, spontaneous_patterns)
        
        # 提取自发性眼震的详细信息
        horizontal_left = re.search(r'水平向：左\s*(\d+)%', text)
        results['自发性眼震_水平向左'] = horizontal_left.group(1) if horizontal_left else ""
        
        horizontal_right = re.search(r'右\s*(\d+)%', text)
        results['自发性眼震_水平向右'] = horizontal_right.group(1) if horizontal_right else ""
        
        vertical_up = re.search(r'垂直向：上\s*(\d+)%', text)
        results['自发性眼震_垂直向上'] = vertical_up.group(1) if vertical_up else ""
        
        vertical_down = re.search(r'下\s*(\d+)%', text)
        results['自发性眼震_垂直向下'] = vertical_down.group(1) if vertical_down else ""
        
        # 凝视试验
        gaze_patterns = [
            r'凝视试验\s*([正异]常)',
            r'3、凝视试验\s*([正异]常)',
        ]
        results['凝视试验'] = self.extract_by_patterns(text, gaze_patterns)
        
        # 平滑跟踪
        smooth_patterns = [
            r'平滑跟踪\s*([IⅠⅡⅢⅣ]+型)',
            r'4、平滑跟踪\s*([IⅠⅡⅢⅣ]+型)',
        ]
        results['平滑跟踪'] = self.extract_by_patterns(text, smooth_patterns)
        
        # 扫视试验
        saccade_patterns = [
            r'扫视试验\s*([正异]常)',
            r'5、扫视试验\s*([正异]常)',
        ]
        results['扫视试验'] = self.extract_by_patterns(text, saccade_patterns)
        
        # 扫视试验详情
        saccade_detail = re.search(r'扫视试验\s*[正异]常[（(]([^）)]+)[）)]', text)
        results['扫视试验_详情'] = saccade_detail.group(1) if saccade_detail else ""
        
        # 视动性眼震
        optokinetic_patterns = [
            r'左右向视动反应：([对不]称)',
            r'视动性眼震.*?([对不]称)',
        ]
        results['视动性眼震'] = self.extract_by_patterns(text, optokinetic_patterns)
        
        return results
    
    def extract_position_tests(self, text):
        """提取位置试验结果"""
        results = {}
        
        # Roll Test
        roll_patterns = [
            r'Roll\s*Test\s*([阴阳]性)',
            r'①Roll\s*Test\s*([阴阳]性)',
        ]
        results['Roll_Test'] = self.extract_by_patterns(text, roll_patterns)
        
        # 翻身试验
        turning_patterns = [
            r'翻身试验\s*([阴阳]性)',
            r'②翻身试验\s*([阴阳]性)',
        ]
        results['翻身试验'] = self.extract_by_patterns(text, turning_patterns)
        
        # Dix-Hallpike左侧
        dix_left_patterns = [
            r'左侧悬头位\s*([阴阳]性)',
            r'Dix-Hallpike.*?左侧悬头位\s*([阴阳]性)',
        ]
        results['Dix_Hallpike_左侧'] = self.extract_by_patterns(text, dix_left_patterns)
        
        # Dix-Hallpike右侧
        dix_right_patterns = [
            r'右侧悬头位\s*([阴阳]性)',
            r'Dix-Hallpike.*?右侧悬头位\s*([阴阳]性)',
        ]
        results['Dix_Hallpike_右侧'] = self.extract_by_patterns(text, dix_right_patterns)
        
        # 疲劳现象
        fatigue_patterns = [
            r'疲劳现象\s*([阴阳]性)',
        ]
        results['疲劳现象'] = self.extract_by_patterns(text, fatigue_patterns)
        
        return results
    
    def extract_diagnosis(self, text):
        """提取诊断信息"""
        results = {}
        
        # 印象
        impression_patterns = [
            r'印象[：:]\s*(.*?)(?:检查者|$)',
            r'印象[：:](.*?)(?:\n.*?检查者|$)',
        ]
        impression = self.extract_by_patterns(text, impression_patterns)
        if impression:
            # 清理印象内容
            impression = re.sub(r'\s+', ' ', impression).strip()
            impression = re.sub(r'[\n\r]+', ' ', impression)
        results['印象'] = impression
        
        # 不再提取检查者字段 - 按用户要求移除

        return results

    def extract_patient_data(self, file_path):
        """提取单个病例文件的完整数据"""
        filename = os.path.basename(file_path)

        # 不再提取年份 - 按用户要求移除

        # 提取文本内容
        text = self.extract_text_from_file(file_path)
        if not text:
            self.log(f"无法提取文本内容: {filename}")
            return None

        # 验证是否为VNG报告
        vng_keywords = ['视频眼震电图', 'VNG', '前庭功能检查']
        if not any(keyword in text for keyword in vng_keywords):
            self.log(f"非VNG报告: {filename}")
            return None

        # 提取各部分数据
        basic_info = self.extract_basic_info(text)
        exam_results = self.extract_examination_results(text)
        position_tests = self.extract_position_tests(text)
        diagnosis = self.extract_diagnosis(text)

        # 合并所有数据 - 不再包含年份
        patient_data = {
            '文件名': filename,
            '原始文本': text[:500] + "..." if len(text) > 500 else text  # 保存前500字符作为参考
        }

        patient_data.update(basic_info)
        patient_data.update(exam_results)
        patient_data.update(position_tests)
        patient_data.update(diagnosis)

        return patient_data

    def validate_data(self, data):
        """验证数据完整性 - 允许部分字段为空"""
        # 只要有姓名或者检查日期其中之一就认为是有效数据
        has_name = bool(data.get('姓名'))
        has_date = bool(data.get('检查日期'))

        # 至少要有姓名或检查日期
        if has_name or has_date:
            return True, []
        else:
            return False, ['姓名', '检查日期']

    def process_files(self):
        """处理所有文件"""
        self.log("开始处理VNG病例文件...")

        # 获取所有文件，排除undefined文件夹
        all_files = []
        for root, dirs, files in os.walk(self.source_dir):
            # 跳过undefined文件夹
            if 'undefined' in root.lower():
                self.log(f"跳过undefined文件夹: {root}")
                continue

            for file in files:
                if file.endswith(('.docx', '.doc', '.wps')):
                    all_files.append(os.path.join(root, file))

        self.stats['total_files'] = len(all_files)
        self.log(f"找到 {len(all_files)} 个文件需要处理")

        # 准备CSV文件 - 移除年份和检查者列，合并位置试验相关列
        csv_headers = [
            '文件名', '姓名', '性别', '年龄', '检查日期', '科别', '门诊住院号', '编号',
            '定标试验', '自发性眼震', '自发性眼震_水平向左', '自发性眼震_水平向右',
            '自发性眼震_垂直向上', '自发性眼震_垂直向下', '凝视试验', '凝视试验_定向',
            '凝视试验_变向', '平滑跟踪', '扫视试验', '扫视试验_详情', '视动性眼震',
            'Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象',
            '印象', '原始文本'
        ]

        successful_data = []

        # 处理每个文件
        for i, file_path in enumerate(all_files, 1):
            filename = os.path.basename(file_path)

            if i % 100 == 0 or i <= 10:
                self.log(f"处理进度: {i}/{len(all_files)} - {filename}")

            try:
                # 提取数据
                patient_data = self.extract_patient_data(file_path)

                if patient_data is None:
                    self.stats['cannot_extract'] += 1
                    # 移动到无法提取文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'cannot_extract', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 验证数据
                is_valid, missing_fields = self.validate_data(patient_data)

                if not is_valid:
                    self.log(f"数据不完整 {filename}: 缺失字段 {missing_fields}")
                    self.stats['format_error'] += 1
                    # 移动到格式错误文件夹
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                    continue

                # 数据成功提取
                successful_data.append(patient_data)
                self.stats['success_count'] += 1

            except Exception as e:
                self.log(f"处理文件异常 {filename}: {e}")
                if self.debug:
                    self.log(traceback.format_exc())
                self.stats['failed_count'] += 1

                # 移动到格式错误文件夹
                try:
                    dest_path = os.path.join(self.output_dir, 'failed_files', 'format_error', filename)
                    shutil.copy2(file_path, dest_path)
                except:
                    pass

        # 写入CSV文件
        if successful_data:
            self.write_csv(successful_data, csv_headers)
            self.log(f"成功提取 {len(successful_data)} 个病例数据")

            # 执行数据清理
            self.clean_and_finalize_data()

        # 生成统计报告
        self.generate_statistics_report()

        return successful_data

    def write_csv(self, data, headers):
        """写入CSV文件"""
        try:
            with open(self.csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=headers, quoting=csv.QUOTE_ALL)
                writer.writeheader()

                for row in data:
                    # 确保所有字段都存在
                    complete_row = {}
                    for header in headers:
                        complete_row[header] = row.get(header, '')
                    writer.writerow(complete_row)

            self.log(f"CSV文件已保存: {self.csv_file}")

        except Exception as e:
            self.log(f"写入CSV文件失败: {e}")

    def clean_name_field(self, name):
        """清理姓名字段"""
        if not name or str(name).strip() == '':
            return ''

        name = str(name).strip()
        name = re.sub(r'^[：:_\s]+', '', name)
        name = re.sub(r'[_\s]+$', '', name)
        name = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf·]', '', name)
        return name.strip()

    def clean_department_field(self, dept):
        """清理科别字段"""
        if not dept or str(dept).strip() == '':
            return ''

        dept = str(dept).strip()
        dept = re.sub(r'^[：:_\s]+', '', dept)
        dept = re.sub(r'[_\s]*\d+床[_\s]*$', '', dept)
        dept = re.sub(r'[_\s]+$', '', dept)
        return dept.strip()

    def clean_hospital_id(self, hospital_id):
        """清理住院号字段"""
        if not hospital_id or str(hospital_id).strip() == '':
            return ''

        hospital_id = str(hospital_id).strip()
        hospital_id = re.sub(r'[^A-Za-z0-9]', '', hospital_id)
        return hospital_id

    def standardize_date(self, date_str):
        """标准化日期格式"""
        if not date_str or str(date_str).strip() == '':
            return ''

        date_str = str(date_str).strip()

        # 处理各种日期格式
        date_patterns = [
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', r'\3-\1-\2'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', r'\1-\2-\3'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1-\2-\3'),
        ]

        for pattern, replacement in date_patterns:
            if re.match(pattern, date_str):
                date_str = re.sub(pattern, replacement, date_str)
                break

        # 确保月份和日期是两位数
        parts = date_str.split('-')
        if len(parts) == 3:
            year, month, day = parts
            try:
                month = f"{int(month):02d}"
                day = f"{int(day):02d}"
                return f"{year}-{month}-{day}"
            except ValueError:
                return date_str

        return date_str

    def clean_impression_field(self, impression):
        """清理印象字段中的乱码"""
        if not impression or str(impression).strip() == '':
            return ''

        impression = str(impression)

        # 查找乱码模式的开始位置
        corruption_start = None
        for pattern in self.corruption_patterns:
            match = re.search(pattern, impression)
            if match:
                if corruption_start is None or match.start() < corruption_start:
                    corruption_start = match.start()

        # 如果找到乱码，截取乱码前的部分
        if corruption_start is not None:
            clean_end = corruption_start
            for i in range(corruption_start - 1, -1, -1):
                if impression[i] in '。，；、':
                    clean_end = i + 1
                    break
                elif impression[i] in '.,;':
                    clean_end = i + 1
                    break
            impression = impression[:clean_end]

        # 移除剩余的乱码
        for pattern in self.corruption_patterns:
            impression = re.sub(pattern, '', impression)

        # 清理多余的空白字符
        impression = re.sub(r'\s+', ' ', impression).strip()
        impression = re.sub(r'[^\u4e00-\u9fff\w\s\.,，。、；：！？()（）\[\]【】]+$', '', impression)

        return impression.strip()

    def merge_position_tests(self, row):
        """合并位置试验相关信息"""
        import pandas as pd

        position_info = []

        # 辅助函数：检查值是否有效
        def is_valid_value(val):
            if pd.isna(val) or val == '' or str(val).lower() in ['nan', 'none']:
                return False
            return True

        # Roll Test / 翻身试验
        roll_test = row.get('Roll_Test', '')
        turning_test = row.get('翻身试验', '')

        roll_valid = is_valid_value(roll_test)
        turning_valid = is_valid_value(turning_test)

        if roll_valid or turning_valid:
            if roll_valid and turning_valid:
                roll_str = str(roll_test).strip()
                turning_str = str(turning_test).strip()
                if roll_str == turning_str:
                    position_info.append(f"①Roll Test/翻身试验: {roll_str}")
                else:
                    position_info.append(f"①Roll Test: {roll_str}")
                    position_info.append(f"翻身试验: {turning_str}")
            elif roll_valid:
                position_info.append(f"①Roll Test: {str(roll_test).strip()}")
            elif turning_valid:
                position_info.append(f"①翻身试验: {str(turning_test).strip()}")

        # Dix-Hallpike试验
        dix_left = row.get('Dix_Hallpike_左侧', '')
        dix_right = row.get('Dix_Hallpike_右侧', '')

        dix_left_valid = is_valid_value(dix_left)
        dix_right_valid = is_valid_value(dix_right)

        if dix_left_valid or dix_right_valid:
            dix_info = "②Dix-Hallpike"
            if dix_left_valid:
                dix_info += f" 左侧悬头位: {str(dix_left).strip()}"
            if dix_right_valid:
                if dix_left_valid:
                    dix_info += f"; 右侧悬头位: {str(dix_right).strip()}"
                else:
                    dix_info += f" 右侧悬头位: {str(dix_right).strip()}"
            position_info.append(dix_info)

        # 疲劳现象
        fatigue = row.get('疲劳现象', '')
        if is_valid_value(fatigue):
            position_info.append(f"疲劳现象: {str(fatigue).strip()}")

        # 如果没有任何位置试验信息，返回空字符串
        if not position_info:
            return ""

        # 合并所有信息
        return "; ".join(position_info)

    def clean_and_finalize_data(self):
        """清理数据并生成最终CSV"""
        import pandas as pd

        self.log("开始数据清理...")

        # 读取刚生成的CSV文件
        df = pd.read_csv(self.csv_file)

        # 清理各个字段
        if '姓名' in df.columns:
            self.log("清理姓名字段...")
            df['姓名'] = df['姓名'].apply(self.clean_name_field)

        if '科别' in df.columns:
            self.log("清理科别字段...")
            df['科别'] = df['科别'].apply(self.clean_department_field)

        if '门诊住院号' in df.columns:
            self.log("清理住院号字段...")
            df['门诊住院号'] = df['门诊住院号'].apply(self.clean_hospital_id)

        if '检查日期' in df.columns:
            self.log("标准化检查日期...")
            df['检查日期'] = df['检查日期'].apply(self.standardize_date)

        if '印象' in df.columns:
            self.log("清理印象字段...")
            df['印象'] = df['印象'].apply(self.clean_impression_field)

        # 合并位置试验相关列
        if all(col in df.columns for col in ['Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象']):
            self.log("合并位置试验相关列...")
            df['位置试验'] = df.apply(self.merge_position_tests, axis=1)

            # 移除原来的单独列
            df = df.drop(['Roll_Test', '翻身试验', 'Dix_Hallpike_左侧', 'Dix_Hallpike_右侧', '疲劳现象'], axis=1)

        # 移除原始文本列
        if '原始文本' in df.columns:
            self.log("移除原始文本列...")
            df = df.drop('原始文本', axis=1)

        # 保存最终清理后的数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_output_file = os.path.join(self.output_dir, f'vng_patient_data_final_{timestamp}.csv')
        df.to_csv(final_output_file, index=False, encoding='utf-8-sig')

        self.log(f"最终清理后的数据已保存到: {final_output_file}")

        # 打印统计信息
        self.log(f"最终数据统计: {len(df)} 行, {len(df.columns)} 列")

    def generate_statistics_report(self):
        """生成统计报告"""
        report_file = os.path.join(self.output_dir, 'statistics_report.txt')

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("VNG病例数据提取统计报告\n")
                f.write("=" * 50 + "\n\n")

                f.write(f"总文件数: {self.stats['total_files']}\n")
                f.write(f"成功提取: {self.stats['success_count']}\n")
                f.write(f"无法提取: {self.stats['cannot_extract']}\n")
                f.write(f"格式错误: {self.stats['format_error']}\n")
                f.write(f"处理异常: {self.stats['failed_count']}\n\n")

                success_rate = (self.stats['success_count'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0
                f.write(f"成功率: {success_rate:.1f}%\n\n")

                if self.stats['year_stats']:
                    f.write("按年份统计:\n")
                    for year in sorted(self.stats['year_stats'].keys()):
                        count = self.stats['year_stats'][year]
                        f.write(f"  {year}年: {count} 个文件\n")

            self.log(f"统计报告已保存: {report_file}")

        except Exception as e:
            self.log(f"生成统计报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='VNG病例信息提取系统')

    # 默认路径
    default_source_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/final_vng_classified"
    default_output_dir = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/output"

    parser.add_argument('--source-dir', default=default_source_dir,
                       help=f'源文件目录路径 (默认: {default_source_dir})')
    parser.add_argument('--output-dir', default=default_output_dir,
                       help=f'输出目录路径 (默认: {default_output_dir})')
    parser.add_argument('--debug', action='store_true',
                       help='显示调试信息')

    args = parser.parse_args()

    print("VNG病例信息提取系统")
    print("=" * 50)
    print(f"源目录: {args.source_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"调试模式: {args.debug}")
    print("-" * 50)

    # 检查源目录
    if not os.path.exists(args.source_dir):
        print(f"错误: 源目录不存在 - {args.source_dir}")
        return 1

    # 创建提取器并处理
    extractor = VNGDataExtractor(args.source_dir, args.output_dir, args.debug)

    try:
        results = extractor.process_files()

        print("\n处理完成!")
        print(f"总文件数: {extractor.stats['total_files']}")
        print(f"成功提取: {extractor.stats['success_count']}")
        print(f"成功率: {extractor.stats['success_count']/extractor.stats['total_files']*100:.1f}%")
        print(f"输出文件: {extractor.csv_file}")

        return 0

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        if args.debug:
            print(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
