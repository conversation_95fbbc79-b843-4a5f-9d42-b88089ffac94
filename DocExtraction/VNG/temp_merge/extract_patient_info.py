#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VEMP气导PDF文件信息提取工具

此脚本用于从VEMP气导检查报告PDF文件中提取患者基本信息，包括：
- 姓名
- 性别
- 年龄
- 科别
- 住院号
- 编号
- 检查日期
- 医生意见

使用方法:
    python extract_patient_info.py [pdf_file_path] [--save-csv] [--debug]
    python extract_patient_info.py --batch [directory_path] [--save-csv] [--debug]
    
    参数：
    pdf_file_path    - PDF文件路径，如果不指定，将使用默认路径
    --batch          - 批量处理模式，处理指定目录下的所有PDF文件
    directory_path   - 包含PDF文件的目录路径，如不指定则使用默认目录
    --save-csv       - 将结果保存到CSV文件中
    --debug          - 显示调试信息
    
作者: [Your Name]
日期: [Current Date]
"""

import os
import re
import sys
import csv
import glob
import traceback
import argparse
import pdfplumber
from pathlib import Path
from datetime import datetime
import tkinter as tk
from tkinter import ttk, scrolledtext
from tkinter import messagebox
# 新增导入 docx
import docx


def extract_patient_info(pdf_path, debug=False):
    """
    从VEMP气导PDF文件或DOCX文件中提取患者信息
    
    参数:
        pdf_path (str): PDF或DOCX文件路径
        debug (bool): 是否打印调试信息
        
    返回:
        dict: 包含患者信息的字典
    """
    # 初始化患者信息字典
    patient_info = {
        "姓名": "",
        "性别": "",
        "年龄": "",
        "科别": "",
        "住院号": "",
        "编号": "",
        "检查日期": "",
        "医生意见": ""
    }
    
    if not os.path.exists(pdf_path):
        print(f"错误: 文件不存在 - {pdf_path}")
        return patient_info
    
    # 判断文件类型
    file_ext = os.path.splitext(pdf_path)[1].lower()
    if file_ext == ".docx":
        # 处理 docx 文件
        try:
            doc = docx.Document(pdf_path)
            # 同时读取段落和表格中的文本
            paragraphs = [p.text for p in doc.paragraphs]
            tables_text = []
            for table in doc.tables:
                for row in table.rows:
                    # 使用制表符连接单元格文本，以保留一定的结构性
                    row_text = "\t".join([cell.text for cell in row.cells])
                    tables_text.append(row_text)
            full_text = "\n".join(paragraphs + tables_text)
            
            if debug:
                print("DOCX全文(前300字符):", full_text[:300])
            # 直接用正则提取各字段
            # 姓名
            if not patient_info["姓名"]:
                name_patterns = [
                    r'姓\s*名\s*[：:]\s*([^\s]+)',
                    r'患者姓名\s*[：:]\s*([^\s]+)',
                ]
                patient_info["姓名"] = extract_by_patterns(full_text, name_patterns)
            
            # 性别
            if not patient_info["性别"]:
                gender_patterns = [
                    r'性\s*别\s*[：:]\s*([男女])',
                    r'性\s*别\s+([男女])',
                    r'Patient sex[：:]\s*([MF男女])'
                ]
                patient_info["性别"] = extract_by_patterns(full_text, gender_patterns)
                if patient_info["性别"] == "M":
                    patient_info["性别"] = "男"
                elif patient_info["性别"] == "F":
                    patient_info["性别"] = "女"
            
            # 年龄
            if not patient_info["年龄"]:
                age_patterns = [
                    r'年\s*龄\s*[：:]\s*(\d+)[岁]?',
                    r'Patient age[：:]\s*(\d+)',
                    r'Age[：:]\s*(\d+)'
                ]
                patient_info["年龄"] = extract_by_patterns(full_text, age_patterns)
            
            # 科别
            if not patient_info["科别"]:
                dept_patterns = [
                    r'科\s*别\s*[：:]\s*([^\s\n]{2,15})',
                    r'科\s*室\s*[：:]\s*([^\s\n]{2,15})',
                    r'检查科室\s*[：:]\s*([^\s\n]{2,15})',
                    r'就诊科室\s*[：:]\s*([^\s\n]{2,15})',
                    r'Department[：:]\s*([^\s\n]{2,20})'
                ]
                patient_info["科别"] = extract_by_patterns(full_text, dept_patterns)
            
            # 住院号
            if not patient_info["住院号"]:
                hospital_id_patterns = [
                    r'住\s*院\s*号\s*[：:]\s*([A-Za-z0-9]+)',
                    r'门诊/住院号\s*[：:]?\s*([A-Za-z0-9]+)',
                    r'门诊号\s*[：:]\s*([A-Za-z0-9]+)',
                    r'病\s*历\s*号\s*[：:]\s*([A-Za-z0-9]+)',
                    r'Patient ID[：:]\s*([A-Za-z0-9]+)',
                    r'ID[：:]\s*([A-Za-z0-9]+)'
                ]
                patient_info["住院号"] = extract_by_patterns(full_text, hospital_id_patterns)
            
            # 编号
            if not patient_info["编号"]:
                id_patterns = [
                    r'编\s*号\s*[：:]\s*([^\s]+)',
                    r'检查编号\s*[：:]\s*([^\s]+)',
                    r'号\s*码\s*[：:]\s*([^\s]+)',
                    r'Number[：:]\s*([^\s]+)',
                    r'NO[.：:]\s*([^\s]+)'
                ]
                patient_info["编号"] = extract_by_patterns(full_text, id_patterns)
            
            # 检查日期
            if not patient_info["检查日期"]:
                date_patterns = [
                    r'检\s*查\s*日\s*期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
                    r'检查时间\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
                    r'日期\s*[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)'
                ]
                patient_info["检查日期"] = extract_by_patterns(full_text, date_patterns)
            if not patient_info["检查日期"]:
                general_date_patterns = [
                    r'(\d{4}-\d{1,2}-\d{1,2})',
                    r'(\d{4}/\d{1,2}/\d{1,2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'
                ]
                patient_info["检查日期"] = extract_by_patterns(full_text, general_date_patterns)
            # 医生意见，优先“印象:”
            if not patient_info["医生意见"]:
                # 优先匹配“印象:”开头到“医师”或文末
                impression_match = re.search(r'印象[:：](.*?)(?:医师|$)', full_text, re.DOTALL)
                if impression_match:
                    patient_info["医生意见"] = impression_match.group(1).strip()
            # 如果还没有，尝试原有的方式
            if not patient_info["医生意见"]:
                opinion_match = re.search(r'(双耳行[co]VEMP检查，给声方式：气导。.*?)医师：', full_text, re.DOTALL)
                if opinion_match:
                    patient_info["医生意见"] = opinion_match.group(1).strip()
            if not patient_info["医生意见"]:
                opinion_match = re.search(r'(给声方式：.*?)\n(.*?)医师：', full_text, re.DOTALL)
                if opinion_match:
                    patient_info["医生意见"] = opinion_match.group(1) + " " + opinion_match.group(2).strip()
            if not patient_info["医生意见"]:
                tip_match = re.search(r'提示:(.+?)(?:\n|$)', full_text)
                if tip_match:
                    patient_info["医生意见"] = tip_match.group(1).strip()
            if patient_info["医生意见"]:
                patient_info["医生意见"] = re.sub(r'[\n\r]+', ' ', patient_info["医生意见"])
                patient_info["医生意见"] = re.sub(r'\s+', ' ', patient_info["医生意见"])
            return patient_info
        except Exception as e:
            print(f"错误: 处理DOCX文件时发生错误: {e}")
            return patient_info
    else:
        # PDF处理逻辑保持原有，但医生意见部分优先“印象:”
        try:
            with pdfplumber.open(pdf_path) as pdf:
                # 获取所有页面的文本内容，有些信息可能在不同页面
                full_text = ""
                all_pages = []
                
                for page in pdf.pages:
                    try:
                        page_text = page.extract_text() or ""
                        full_text += page_text + "\n"
                        all_pages.append(page)
                    except Exception as e:
                        print(f"警告: 提取第{page.page_number}页文本时出错: {e}")
                
                if not full_text:
                    print("警告: 未能从PDF中提取任何文本")
                    return patient_info
                    
                # 打印调试信息
                if debug:
                    print("提取的PDF文本(前300个字符):")
                    print(full_text[:300])
                    print("---------------")
                    print("提取的完整PDF文本:")
                    print(full_text)
                    print("---------------")
                    
                # 使用新的特殊字段提取函数来尝试获取科别和住院号
                special_fields = extract_special_fields(full_text, debug)
                
                # 首先尝试从表格结构中提取信息
                if all_pages:
                    first_page = all_pages[0]  # 患者基本信息通常在第一页
                    
                    # 尝试从表格中提取信息
                    if not patient_info["姓名"]:
                        name_from_table = extract_field_from_table(first_page, "姓名", debug)
                        if name_from_table:
                            patient_info["姓名"] = name_from_table
                    
                    if not patient_info["性别"]:
                        gender_from_table = extract_field_from_table(first_page, "性别", debug)
                        if gender_from_table:
                            # 只保留第一个字符，通常是"男"或"女"
                            patient_info["性别"] = gender_from_table[0] if gender_from_table else ""
                    
                    if not patient_info["年龄"]:
                        age_from_table = extract_field_from_table(first_page, "年龄", debug)
                        if age_from_table:
                            # 提取数字部分
                            age_match = re.search(r'\d+', age_from_table)
                            if age_match:
                                patient_info["年龄"] = age_match.group(0)
                    
                    if not patient_info["科别"]:
                        dept_from_table = extract_field_from_table(first_page, "科别", debug)
                        if not dept_from_table:
                            dept_from_table = extract_field_from_table(first_page, "科室", debug)
                        if dept_from_table:
                            patient_info["科别"] = dept_from_table
                        # 如果表格中未找到，尝试使用特殊提取的科别信息
                        elif special_fields["科别"]:
                            patient_info["科别"] = special_fields["科别"]
                
                # 提取所需信息
                # 只有当 patient_info 中的字段为空时，才使用正则表达式进行提取
                
                # 姓名 - 多种可能的格式
                if not patient_info["姓名"]:
                    name_patterns = [
                        r'姓名[：:\s]*([^\s]+)',
                        r'姓\s*名[：:\s]*([^\s]+)',
                        r'患者姓名[：:\s]*([^\s]+)',
                    ]
                    patient_info["姓名"] = extract_by_patterns(full_text, name_patterns)
                
                # 性别
                if not patient_info["性别"]:
                    gender_patterns = [
                        r'性别[：:\s]\s*([男女])',
                        r'性\s*别[：:\s]\s*([男女])',
                        r'性别：?\s*([男女])',
                        r'性别:\s*([男女])',
                        r'性\s+别\s*[：:\s]\s*([男女])',
                        r'性\s+别\s*:\s*([男女])',
                        r'Patient sex[：:]\s*([MF男女])'
                    ]
                    patient_info["性别"] = extract_by_patterns(full_text, gender_patterns)
                    # 如果找到英文性别，转换为中文
                    if patient_info["性别"] == "M":
                        patient_info["性别"] = "男"
                    elif patient_info["性别"] == "F":
                        patient_info["性别"] = "女"
                
                # 年龄
                if not patient_info["年龄"]:
                    age_patterns = [
                        r'年龄[：:\s]\s*(\d+)[岁]?',
                        r'年\s*龄[：:\s]\s*(\d+)[岁]?',
                        r'年龄：?\s*(\d+)[岁]?',
                        r'年龄:\s*(\d+)[岁]?',
                        r'年\s+龄\s*[：:\s]\s*(\d+)[岁]?',
                        r'年\s+龄\s*:\s*(\d+)[岁]?',
                        r'Patient age[：:]\s*(\d+)',
                        r'Age[：:]\s*(\d+)'
                    ]
                    patient_info["年龄"] = extract_by_patterns(full_text, age_patterns)
                
                # 科别
                if not patient_info["科别"]:
                    dept_patterns = [
                        r'科别[：:\s]\s*(.*?)\s*(?:门诊/住院号|编号|检\s*查\s*日\s*期|ID)', # 优先匹配直到下一个字段
                        r'科别[：:\s]\s*([^\s\n]{2,15})',
                        r'科\s*别[：:\s]\s*([^\s\n]{2,15})',
                        r'科室[：:\s]\s*([^\s\n]{2,15})',
                        r'科别：?\s*([^\s\n]{2,15})',
                        r'科室：?\s*([^\s\n]{2,15})',
                        r'科\s+别\s*[：:\s]\s*([^\s\n]{2,15})',
                        r'检查科室[：:\s]\s*([^\s\n]{2,15})',
                        r'就诊科室[：:\s]\s*([^\s\n]{2,15})',
                        r'Department[：:]\s*([^\s\n]{2,20})'
                    ]
                    patient_info["科别"] = extract_by_patterns(full_text, dept_patterns)
                
                # 住院号
                if not patient_info["住院号"]:
                    hospital_id_patterns = [
                        r'住院号[：:\s]\s*([A-Za-z0-9]+)',
                        r'住\s*院\s*号[：:\s]\s*([A-Za-z0-9]+)',
                        r'住院号：?\s*([A-Za-z0-9]+)',
                        r'门诊/住院号[：:\s]?\s*([A-Za-z0-9]+)',
                        r'门诊号[：:\s]\s*([A-Za-z0-9]+)',
                        r'住院号:\s*([A-Za-z0-9]+)',
                        r'住\s+院\s+号[：:\s]\s*([A-Za-z0-9]+)',
                        r'病\s*历\s*号[：:\s]\s*([A-Za-z0-9]+)',
                        r'Patient ID[：:\s]\s*([A-Za-z0-9]+)',
                        r'ID[：:\s]\s*([A-Za-z0-9]+)'
                    ]
                    patient_info["住院号"] = extract_by_patterns(full_text, hospital_id_patterns)
                
                # 如果还未找到住院号，尝试从表格中提取
                if not patient_info["住院号"] and all_pages:
                    hospital_id_from_table = extract_field_from_table(all_pages[0], "住院号", debug)
                    if not hospital_id_from_table:
                        hospital_id_from_table = extract_field_from_table(all_pages[0], "门诊号", debug)
                    if not hospital_id_from_table:
                        hospital_id_from_table = extract_field_from_table(all_pages[0], "病历号", debug)
                    if not hospital_id_from_table:
                        hospital_id_from_table = extract_field_from_table(all_pages[0], "ID", debug)
                    if hospital_id_from_table:
                        patient_info["住院号"] = hospital_id_from_table
                    # 如果表格中未找到，尝试使用特殊提取的住院号信息
                    elif special_fields["住院号"]:
                        patient_info["住院号"] = special_fields["住院号"]
                
                # 编号
                if not patient_info["编号"]:
                    id_patterns = [
                        r'编号[：:]\s*([^\s]+)',
                        r'编\s*号[：:]\s*([^\s]+)',
                        r'编号：?\s*([0-9]+)',
                        r'编号:\s*([^\s]+)',
                        r'编\s+号[：:]\s*([^\s]+)',
                        r'检查编号[：:]\s*([^\s]+)',
                        r'检\s*查\s*编\s*号[：:]\s*([^\s]+)',
                        r'号\s*码[：:]\s*([^\s]+)',
                        r'Number[：:]\s*([^\s]+)',
                        r'NO[.：:]\s*([^\s]+)'
                    ]
                    patient_info["编号"] = extract_by_patterns(full_text, id_patterns)
                
                # 如果还未找到编号，尝试从表格中提取
                if not patient_info["编号"] and all_pages:
                    id_from_table = extract_field_from_table(all_pages[0], "编号", debug)
                    if not id_from_table:
                        id_from_table = extract_field_from_table(all_pages[0], "检查编号", debug)
                    if not id_from_table:
                        id_from_table = extract_field_from_table(all_pages[0], "号码", debug)
                    if id_from_table:
                        patient_info["编号"] = id_from_table
                
                # 检查日期 - 多种可能的日期格式
                if not patient_info["检查日期"]:
                    date_patterns = [
                        r'检查日期[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
                        r'检查日期[：:]\s*(\d{4}年\d{1,2}月\d{1,2}日)',
                        r'检\s*查\s*日\s*期[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
                        r'检查时间[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)',
                        r'日期[：:]\s*(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)'
                    ]
                    patient_info["检查日期"] = extract_by_patterns(full_text, date_patterns)
                
                # 如果还没找到日期，尝试找任何符合日期格式的内容
                if not patient_info["检查日期"]:
                    general_date_patterns = [
                        r'(\d{4}-\d{1,2}-\d{1,2})',
                        r'(\d{4}/\d{1,2}/\d{1,2})',
                        r'(\d{4}年\d{1,2}月\d{1,2}日)'
                    ]
                    patient_info["检查日期"] = extract_by_patterns(full_text, general_date_patterns)
                
                # 尝试使用更通用的方法提取医生意见
                if not patient_info["医生意见"]:
                    # 根据实际PDF内容，包含"双耳行cVEMP检查，给声方式：气导。"开头部分
                    opinion_match = re.search(r'(双耳行[co]VEMP检查，给声方式：气导。.*?)医师：', full_text, re.DOTALL)
                    if opinion_match:
                        patient_info["医生意见"] = opinion_match.group(1).strip()
                
                # 如果未找到上面的模式，尝试更宽松的模式
                if not patient_info["医生意见"]:
                    # 尝试提取从"给声方式"的内容到"医师"前面的内容
                    opinion_match = re.search(r'(给声方式：.*?)\n(.*?)医师：', full_text, re.DOTALL)
                    if opinion_match:
                        patient_info["医生意见"] = opinion_match.group(1) + " " + opinion_match.group(2).strip()
                
                # 如果仍然未找到，尝试提取任何包含"提示"的行
                if not patient_info["医生意见"]:
                    tip_match = re.search(r'提示:(.+?)(?:\n|$)', full_text)
                    if tip_match:
                        patient_info["医生意见"] = tip_match.group(1).strip()
                
                # 清理医生意见中的多余空白字符及错误文本
                if patient_info["医生意见"]:
                    # 删除医生意见中出现的" 1 1 "文本（PDF文本提取错误）
                    patient_info["医生意见"] = re.sub(r'\s+1\s+1\s+', ' ', patient_info["医生意见"])
                    
                    # 替换医生意见中的换行符为空格
                    patient_info["医生意见"] = re.sub(r'[\n\r]+', ' ', patient_info["医生意见"])
                    
                    # 替换多个空白字符为单个空格
                    patient_info["医生意见"] = re.sub(r'\s+', ' ', patient_info["医生意见"])
                    
                    # 修正特定错误文本
                    patient_info["医生意见"] = re.sub(r'非对称比\s+为', '非对称比为', patient_info["医生意见"])
        except Exception as e:
            print(f"错误: 处理PDF文件时发生错误: {e}")
            
        return patient_info


def extract_by_patterns(text, patterns):
    """
    使用多个正则表达式模式从文本中提取信息
    
    参数:
        text (str): 要搜索的文本
        patterns (list): 正则表达式模式列表
        
    返回:
        str: 提取的信息，如果未找到则返回空字符串
    """
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            return match.group(1)
    return ""


def extract_field_from_table(page, field_name, debug=False):
    """
    从PDF表格结构中提取特定字段的值
    
    参数:
        page: pdfplumber页面对象
        field_name: 要查找的字段名称
        debug: 是否显示调试信息
        
    返回:
        str: 提取的字段值，如果未找到则返回空字符串
    """
    try:
        tables = page.extract_tables()
        if debug:
            print(f"提取到 {len(tables)} 个表格")
        
        for table in tables:
            for row in table:
                for i, cell in enumerate(row):
                    if cell and field_name in str(cell):
                        # 如果在单元格中找到字段名，尝试获取下一个单元格的值
                        if i + 1 < len(row) and row[i + 1]:
                            return str(row[i + 1]).strip()
        
        # 如果在表格中找不到，尝试查找包含该字段的文本行
        text_lines = page.extract_text().split('\n')
        for line in text_lines:
            if field_name in line:
                # 尝试提取字段值（假设字段名后面跟着冒号和值）
                parts = line.split(field_name + ':', 1)
                if len(parts) > 1:
                    return parts[1].strip()
                
                parts = line.split(field_name + '：', 1)
                if len(parts) > 1:
                    return parts[1].strip()
    except Exception as e:
        if debug:
            print(f"从表格提取 {field_name} 时出错: {e}")
    
    return ""


def process_pdf_files(file_paths, debug=False, save_csv=False, progress_callback=None):
    """
    处理多个PDF文件
    
    参数:
        file_paths (list): PDF文件路径列表
        debug (bool): 是否打印调试信息
        save_csv (bool): 是否保存到CSV文件
        progress_callback (callable): 进度回调函数
        
    返回:
        list: 包含(文件路径, 患者信息字典)元组的列表
    """
    results = []
    failed_files = []
    
    total_files = len(file_paths)
    processed_files = 0
    
    print(f"开始处理 {total_files} 个PDF文件...")
    
    for path in file_paths:
        processed_files += 1
        
        # 调用进度回调
        if progress_callback:
            progress_callback(processed_files, total_files)
            
        print(f"[{processed_files}/{total_files}] 处理文件: {os.path.basename(path)}")
        
        try:
            info = extract_patient_info(path, debug)
            
            # 检查是否成功提取到关键信息
            if not info["姓名"] and not info["检查日期"]:
                print(f"警告: 未能从文件 {os.path.basename(path)} 提取到姓名和检查日期，可能非标准格式")
            
            results.append((path, info))
            
            # 打印结果
            print("提取的患者信息:")
            for key, value in info.items():
                if key == "医生意见" and value:
                    print(f"{key}:")
                    # 根据换行符、分号和句号来分行显示
                    # 先用正则表达式替换特定模式，避免错误分割数值
                    cleaned_value = re.sub(r'(\d+\.\d+)', lambda m: m.group(1).replace('.', '_dot_'), value)
                    # 然后按句号等标点分割
                    lines = re.split(r'([。;；.!！?？\n])', cleaned_value)
                    current_line = ""
                    for i in range(0, len(lines)):
                        if lines[i]:
                            current_line += lines[i]
                            # 如果是标点符号，则完成一行
                            if i % 2 == 1 and i > 0:
                                # 恢复被替换的小数点
                                current_line = current_line.replace('_dot_', '.')
                                current_line = current_line.strip()
                                if current_line:
                                    print(f"  - {current_line}")
                                current_line = ""
                    
                    # 处理最后一部分
                    if current_line:
                        current_line = current_line.replace('_dot_', '.').strip()
                        print(f"  - {current_line}")
                else:
                    print(f"{key}: {value}")
            
        except Exception as e:
            print(f"错误: 处理文件 {os.path.basename(path)} 时出错:")
            print(f"      {str(e)}")
            if debug:
                print(traceback.format_exc())
            failed_files.append(path)
            continue
            
        print("-" * 30)
    
    # 打印处理统计
    print(f"\n处理完成:")
    print(f"- 总文件数: {total_files}")
    print(f"- 成功处理: {len(results)}")
    print(f"- 处理失败: {len(failed_files)}")
    
    if failed_files:
        print("\n处理失败的文件:")
        for file_path in failed_files:
            print(f"- {os.path.basename(file_path)}")
    
    # 保存到CSV文件
    if save_csv and results:
        csv_path = save_to_csv(results)
        print(f"\n结果已保存到: {csv_path}")
    
    return results


def save_to_csv(results):
    """
    将提取的患者信息保存到CSV文件
    
    参数:
        results (list): 包含(文件路径, 患者信息字典)元组的列表
        
    返回:
        str: 保存的CSV文件路径
    """
    # 生成带有时间戳的CSV文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = os.path.join(os.path.dirname(__file__), f"patient_info_{timestamp}.csv")
    
    # 确定CSV列名（标题行）
    if results:
        fieldnames = ["文件名"] + list(results[0][1].keys())
        
        # 写入CSV文件
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
                writer.writeheader()
                
                for path, info in results:
                    # 再次确保医生意见没有换行符等特殊字符，避免CSV格式问题
                    if info["医生意见"]:
                        info["医生意见"] = (info["医生意见"]
                                         .replace("\n", " ")
                                         .replace("\r", " ")
                                         .replace("  ", " ")
                                         .strip())
                    
                    # 将文件名和患者信息合并成一行
                    row = {"文件名": os.path.basename(path)}
                    row.update(info)
                    writer.writerow(row)
                
            return csv_file
        except Exception as e:
            print(f"保存CSV文件时出错: {e}")
    else:
        print("没有数据可以保存")
    
    return None


def parse_arguments():
    """
    解析命令行参数
    
    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='从VEMP气导PDF文件中提取患者信息')
    parser.add_argument('pdf_path_or_dir', nargs='?', default=None,
                        help='PDF文件路径 (单文件模式) 或 包含PDF文件的目录路径 (批量模式). '
                             '如果未指定，将使用默认路径。')
    parser.add_argument('--batch', action='store_true',
                        help='启用批量处理模式。如果设置此参数，pdf_path_or_dir应为目录。')
    parser.add_argument('--save-csv', action='store_true', default=True,
                        help='将结果保存到CSV文件 (默认启用)')
    parser.add_argument('--debug', action='store_true',
                        help='显示调试信息')
    parser.add_argument('--gui', action='store_true',
                        help='启用GUI界面（在某些系统上可能导致卡死），默认禁用')
    
    return parser.parse_args()


def run_extraction_process(file_paths, save_csv=True, debug=False, log_file_path=None, use_gui=True):
    """
    运行PDF文件信息提取过程。

    参数:
        file_paths (list): 要处理的PDF文件路径列表。
        save_csv (bool): 是否将结果保存到CSV文件。默认为True。
        debug (bool): 是否打印调试信息。默认为False。
        log_file_path (str, optional): 日志文件路径，如果提供则将所有打印输出重定向到此文件。
        use_gui (bool): 是否使用GUI界面。
    """
    original_stdout = sys.stdout
    log_file = None

    root = None
    if use_gui:
        # GUI setup
        root = tk.Tk()
        root.withdraw()  # Hide the main window

    try:
        if log_file_path:
            log_dir = os.path.dirname(log_file_path)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            log_file = open(log_file_path, 'w', encoding='utf-8-sig')
            sys.stdout = log_file

        # --- 新的带GUI的批量处理逻辑 ---
        successful_results = []
        failed_files_info = []
        
        # 定义必须提取成功的关键字段。 "编号" 和 "医生意见" 在此被视为可选。
        ESSENTIAL_KEYS = ["姓名", "性别", "年龄", "科别", "住院号", "检查日期"]

        total_files = len(file_paths)
        
        progress_window = None
        if use_gui:
            # 创建进度条窗口
            progress_window = tk.Toplevel(root)
            progress_window.title("处理中...")
            progress_window.geometry("400x150")
            
            progress_label = tk.Label(progress_window, text="正在准备处理文件...", wraplength=380)
            progress_label.pack(pady=10)
            
            progress_bar = ttk.Progressbar(progress_window, orient="horizontal", length=300, mode="determinate")
            progress_bar.pack(pady=10)
            
            count_label = tk.Label(progress_window, text=f"0 / {total_files}")
            count_label.pack(pady=5)

        for i, path in enumerate(file_paths):
            if use_gui and progress_window:
                # 更新进度条
                progress_label.config(text=f"正在处理: {os.path.basename(path)}")
                progress_bar['value'] = (i + 1) / total_files * 100
                count_label.config(text=f"{i + 1} / {total_files}")
                progress_window.update_idletasks()
            else:
                print(f"[{i + 1}/{total_files}] 正在处理: {os.path.basename(path)}")

            try:
                info = extract_patient_info(path, debug)
                
                # 验证关键信息是否提取成功
                missing_keys = [key for key in ESSENTIAL_KEYS if not info.get(key)]
                
                if not missing_keys:
                    successful_results.append((path, info))
                else:
                    failed_files_info.append((os.path.basename(path), missing_keys))

            except Exception as e:
                error_msg = f"处理文件 {os.path.basename(path)} 时发生意外错误: {e}"
                print(error_msg)
                if debug:
                    print(traceback.format_exc())
                failed_files_info.append((os.path.basename(path), ["处理异常"]))

        if use_gui and progress_window:
            progress_window.destroy()

        # 保存成功提取的结果到CSV
        if save_csv and successful_results:
            csv_path = save_to_csv(successful_results)
            print(f"\n{len(successful_results)} 个文件的信息已成功提取并保存到: {csv_path}")
        
        # 如果有失败的文件，弹出总结窗口
        if use_gui:
            if failed_files_info:
                show_summary_window(failed_files_info, root)
            else:
                messagebox.showinfo("处理完成", f"全部 {total_files} 个文件都已成功处理！")
        else:
            if failed_files_info:
                print("\n--- 提取失败的文件摘要 ---")
                for filename, missing_keys in failed_files_info:
                    print(f"文件名: {filename}")
                    print(f"  - 缺失或无法识别的字段: {', '.join(missing_keys)}\n")
                print("--- 摘要结束 ---")
            else:
                print(f"\n全部 {total_files} 个文件都已成功处理！")

    except Exception as e:
        print(f"运行过程中发生未预期的错误: {e}")
        if debug:
            print(traceback.format_exc())
    finally:
        if log_file:
            sys.stdout = original_stdout # 恢复标准输出
            log_file.close()
        if use_gui and root:
            root.destroy()


def show_summary_window(failed_files_info, root):
    """
    创建一个窗口来显示提取失败的文件列表。

    参数:
        failed_files_info (list): 包含(文件名, 缺失字段列表)元组的列表。
        root (tk.Tk): Tkinter根窗口。
    """
    summary_window = tk.Toplevel(root)
    summary_window.title("提取失败的文件 - 请手动检查")
    summary_window.geometry("600x400")

    # Make window modal
    summary_window.transient(root)
    summary_window.grab_set()

    main_frame = ttk.Frame(summary_window, padding="10")
    main_frame.pack(fill="both", expand=True)

    info_label = ttk.Label(main_frame, text="以下PDF文件未能完整提取关键信息，请手动检查文件格式或内容：", wraplength=580)
    info_label.pack(fill="x", pady=(0, 10))

    text_area = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, height=15)
    text_area.pack(fill="both", expand=True)

    report_text = ""
    for filename, missing_keys in failed_files_info:
        report_text += f"文件名: {filename}\n"
        report_text += f"  - 缺失或无法识别的字段: {', '.join(missing_keys)}\n\n"
    
    text_area.insert(tk.END, report_text)
    text_area.config(state="disabled")

    ok_button = ttk.Button(main_frame, text="确定", command=summary_window.destroy)
    ok_button.pack(pady=10)

    root.wait_window(summary_window)


def extract_special_fields(full_text, debug=False):
    """
    针对科别和住院号等特殊字段的提取
    
    参数:
        full_text (str): 完整的PDF文本
        debug (bool): 是否打印调试信息
        
    返回:
        dict: 包含提取到的特殊字段信息
    """
    result = {"科别": "", "住院号": ""}
    
    # 尝试从文本块中提取科别信息
    dept_blocks = [
        # 常见的科室名称列表
        "神经内科", "神经外科", "耳鼻咽喉科", "眼科", "内科", "外科", "儿科", 
        "妇产科", "骨科", "皮肤科", "泌尿外科", "心血管内科", "呼吸科", 
        "消化科", "肿瘤科", "康复科", "精神科", "急诊科", "重症医学科"
    ]
    
    # 查找科别信息
    for dept in dept_blocks:
        if dept in full_text:
            # 确保找到的是科室名称而不是描述的一部分
            lines = full_text.split("\n")
            for line in lines:
                if dept in line:
                    # 如果行中同时包含"科别"或"科室"和科室名称
                    if "科别" in line or "科室" in line:
                        result["科别"] = dept
                        break
                    # 如果行中只有科室名称，并且行较短（可能是表格单元格）
                    elif len(line.strip()) < 15:
                        result["科别"] = dept
                        break
            
            # 如果已经找到科别，跳出循环
            if result["科别"]:
                break
    
    # 尝试从文本块中提取住院号/门诊号
    # 查找任何看起来像住院号或门诊号的数字序列
    id_matches = re.findall(r'[A-Za-z]*\d{5,}', full_text)
    
    # 筛选可能的住院号
    for id_match in id_matches:
        # 检查这个数字序列前后的文本，看是否与住院号相关
        context_start = max(0, full_text.find(id_match) - 20)
        context_end = min(len(full_text), full_text.find(id_match) + len(id_match) + 20)
        context = full_text[context_start:context_end]
        
        if any(keyword in context for keyword in ["住院", "门诊", "病历", "编号", "ID", "号码"]):
            result["住院号"] = id_match
            break
    
    if debug and (result["科别"] or result["住院号"]):
        print("通过特殊方法找到的字段:")
        if result["科别"]:
            print(f"科别: {result['科别']}")
        if result["住院号"]:
            print(f"住院号: {result['住院号']}")
    
    return result


def extract_from_structured_table(pdf_path, debug=False):
    """
    从结构化表格中提取患者信息
    
    一些PDF文件中的信息是按照固定表格布局排列的，
    此函数尝试定位并提取这种格式的信息
    
    参数:
        pdf_path (str): PDF文件路径
        debug (bool): 是否打印调试信息
        
    返回:
        dict: 包含提取到的信息
    """
    result = {"姓名": "", "性别": "", "年龄": "", "科别": "", "住院号": ""}
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            if not pdf.pages:
                return result
                
            first_page = pdf.pages[0]
            
            # 提取所有文本行
            try:
                text = first_page.extract_text()
                if not text:
                    return result
                    
                lines = text.split('\n')
                
                # 尝试查找表格化的行，通常包含患者基本信息
                # 这些行通常有一定的结构，如姓名和性别在同一行
                for line in lines:
                    # 匹配姓名和性别在同一行的模式
                    name_gender_match = re.search(r'姓名[：:]?\s*([^\s]+).*?性别[：:]?\s*([男女])', line)
                    if name_gender_match:
                        result["姓名"] = name_gender_match.group(1)
                        result["性别"] = name_gender_match.group(2)
                        
                    # 匹配年龄和科别在同一行的模式
                    age_dept_match = re.search(r'年龄[：:]?\s*(\d+).*?科[别室][：:]?\s*([^\s]+)', line)
                    if age_dept_match:
                        result["年龄"] = age_dept_match.group(1)
                        result["科别"] = age_dept_match.group(2)
                        
                    # 匹配可能包含住院号的行
                    if "住院" in line or "门诊" in line or "病历" in line:
                        # 尝试提取数字序列
                        id_match = re.search(r'[：:]\s*([A-Za-z0-9]{5,})', line)
                        if id_match:
                            result["住院号"] = id_match.group(1)
                            
                # 尝试使用表格坐标提取信息
                tables = first_page.extract_tables()
                
                if tables:
                    # 检查每个表格的第一行，寻找表头和对应的值
                    for table in tables:
                        if not table or len(table) < 1:
                            continue
                            
                        # 遍历表格的每一行
                        for row in table:
                            if not row:
                                continue
                                
                            # 检查行中的单元格
                            for i, cell in enumerate(row):
                                if not cell:
                                    continue
                                    
                                cell_text = str(cell).strip()
                                
                                # 匹配常见表头
                                if "姓名" in cell_text and i + 1 < len(row) and row[i + 1]:
                                    result["姓名"] = str(row[i + 1]).strip()
                                elif "性别" in cell_text and i + 1 < len(row) and row[i + 1]:
                                    gender = str(row[i + 1]).strip()
                                    if gender:
                                        result["性别"] = gender[0]  # 只取第一个字符
                                elif "年龄" in cell_text and i + 1 < len(row) and row[i + 1]:
                                    age_text = str(row[i + 1]).strip()
                                    age_match = re.search(r'\d+', age_text)
                                    if age_match:
                                        result["年龄"] = age_match.group(0)
                                elif ("科别" in cell_text or "科室" in cell_text) and i + 1 < len(row) and row[i + 1]:
                                    result["科别"] = str(row[i + 1]).strip()
                                elif ("住院号" in cell_text or "门诊号" in cell_text or "病历号" in cell_text) and i + 1 < len(row) and row[i + 1]:
                                    result["住院号"] = str(row[i + 1]).strip()
            except Exception as e:
                if debug:
                    print(f"从结构化表格提取信息时出错: {e}")
                    if hasattr(e, "__traceback__"):
                        import traceback
                        print(traceback.format_tb(e.__traceback__))
    except Exception as e:
        if debug:
            print(f"打开PDF文件进行结构化提取时出错: {e}")
    
    # 打印提取结果
    if debug and any(result.values()):
        print("从结构化表格中提取的信息:")
        for key, value in result.items():
            if value:
                print(f"  {key}: {value}")
    
    return result


def find_files(directory):
    """
    查找指定目录下的所有PDF和DOCX文件
    
    参数:
        directory (str): 要搜索的目录路径
        
    返回:
        list: PDF和DOCX文件路径列表
    """
    if not os.path.exists(directory):
        print(f"错误: 目录不存在 - {directory}")
        return []
    
    if not os.path.isdir(directory):
        print(f"错误: 指定路径不是目录 - {directory}")
        return []
    
    # 查找所有PDF和DOCX文件
    pdf_files = glob.glob(os.path.join(directory, "*.pdf"))
    docx_files = glob.glob(os.path.join(directory, "*.docx"))
    all_files = pdf_files + docx_files
    
    # 按文件名排序
    all_files.sort()
    
    return all_files


def main():
    args = parse_arguments()

    # 默认加载的文件路径改为指定 docx
    default_docx_file = "/Users/<USER>/Documents/PyCharm/MacPythonEnv/Yu/DocExtraction/VNG/data/8李鹏辉.docx"
    script_dir = Path(__file__).parent
    default_data_dir = script_dir / "data_selected"
    default_pdf_file = default_data_dir / "01307林湧茂oVEMP.pdf"

    file_paths = []
    
    # 根据参数和默认值确定处理模式
    if args.batch:
        directory_to_process = args.pdf_path_or_dir or str(default_data_dir)
        print(f"批量处理模式。目标目录: {directory_to_process}")
        file_paths = find_files(directory_to_process)
    else:
        pdf_file_to_process = args.pdf_path_or_dir or default_docx_file
        path_obj = Path(pdf_file_to_process)
        if path_obj.is_file():
            print(f"单文件处理模式。目标文件: {pdf_file_to_process}")
            file_paths = [pdf_file_to_process]
        elif path_obj.is_dir():
            print(f"警告: 您提供了一个目录路径 '{pdf_file_to_process}'，但未启用批量模式(--batch)。")
            print("将自动切换到批量模式处理该目录下的所有PDF和DOCX文件。")
            file_paths = find_files(str(path_obj))
        else:
            print(f"错误: 指定的路径既不是有效文件也不是目录: {pdf_file_to_process}")
            return
    if not file_paths:
        print("错误: 未找到任何要处理的文件。")
        return
    print(f"总计找到 {len(file_paths)} 个文件，开始处理...")
    run_extraction_process(
        file_paths,
        save_csv=args.save_csv,
        debug=args.debug,
        use_gui=args.gui
    )


if __name__ == "__main__":
    main() 