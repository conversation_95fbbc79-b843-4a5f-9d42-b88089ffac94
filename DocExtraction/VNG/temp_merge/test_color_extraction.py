#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试颜色标记提取功能
"""

import os
from vng_data_extractor import VNGDataExtractor

def test_color_extraction():
    """测试颜色标记提取"""
    print("测试颜色标记提取功能")
    print("=" * 50)
    
    # 测试格式错误文件夹中的文件
    test_files = [
        "../output/failed_files/format_error/188 华雪济.docx",
        "../output/failed_files/format_error/226 苏丽娟.docx",
        "../output/failed_files/format_error/257 谭锡媛.docx"
    ]
    
    output_dir = "../test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    extractor = VNGDataExtractor("", output_dir, debug=True)
    
    for file_path in test_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            print(f"\n处理文件: {filename}")
            print("-" * 40)
            
            try:
                patient_data = extractor.extract_patient_data(file_path)
                
                if patient_data:
                    print("提取成功！")
                    for key, value in patient_data.items():
                        if key != '原始文本' and value:  # 只显示非空字段
                            print(f"{key}: {value}")
                    
                    # 验证数据
                    is_valid, missing_fields = extractor.validate_data(patient_data)
                    print(f"\n数据验证: {'通过' if is_valid else '失败'}")
                    if not is_valid:
                        print(f"缺失字段: {missing_fields}")
                else:
                    print("提取失败")
                    
            except Exception as e:
                print(f"处理异常: {e}")
                import traceback
                print(traceback.format_exc())
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    test_color_extraction()
